Parsed input: {"novel_id":2,"chapter_number":37,"target_language":"en"}
Starting translate - Novel ID: 2, Chapters: 37, Language: en
TranslationService: Starting chapter translation - Novel ID: 2, Chapter ID: 313
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Processing furigana content
TranslationService: Chapter found - Title: 37　放出とクロワッサン
TranslationService: Chapter content length: 8285
TranslationService: getNameDictionary query returned 46 entries for novel 2
TranslationService: Name dictionary entries: 46
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 46 entries
DeepSeekTranslationService: Name mapping - レイ → Rei
DeepSeekTranslationService: Name mapping - 近藤哲也 → Tetsuya Kondo
DeepSeekTranslationService: Name mapping - 夏川レイ → Rei Natsukawa
DeepSeekTranslationService: Name mapping - 後藤 → Goto
DeepSeekTranslationService: Name mapping - アキラ → Akira
DeepSeekTranslationService: Name mapping - 佐々木さん → Sasaki-san
DeepSeekTranslationService: Name mapping - 斎藤君 → Saito-kun
DeepSeekTranslationService: Name mapping - レイちゃん → Rei-chan
DeepSeekTranslationService: Name mapping - お爺ちゃん → Ojichan
DeepSeekTranslationService: Name mapping - 加藤 → Kato
DeepSeekTranslationService: Name mapping - 中田 → Nakata
DeepSeekTranslationService: Name mapping - 斎藤 → Saito
DeepSeekTranslationService: Name mapping - 接客室 → Reception Room
DeepSeekTranslationService: Name mapping - 新宿 → Shinjuku
DeepSeekTranslationService: Name mapping - 事務所 → Office
DeepSeekTranslationService: Name mapping - 池袋 → Ikebukuro
DeepSeekTranslationService: Name mapping - 喫茶店 → Café
DeepSeekTranslationService: Name mapping - 東京駅 → Tokyo Station
DeepSeekTranslationService: Name mapping - キカワ工業 → Kikawa Industries
DeepSeekTranslationService: Name mapping - スナック由紀子 → Snack Yukiko
DeepSeekTranslationService: Name mapping - ＪＲ → JR (Japan Railways)
DeepSeekTranslationService: Name mapping - フランス → France
DeepSeekTranslationService: Name mapping - 血管拡張 → Blood Vessel Expansion
DeepSeekTranslationService: Name mapping - 記憶阻害 → Memory Inhibition
DeepSeekTranslationService: Name mapping - 解毒 → Detoxification
DeepSeekTranslationService: Name mapping - 治癒 → Healing
DeepSeekTranslationService: Name mapping - 火魔法 → Fire Magic
DeepSeekTranslationService: Name mapping - 治癒魔法 → Healing Magic
DeepSeekTranslationService: Name mapping - 魔力 → Magic Power
DeepSeekTranslationService: Name mapping - 佐々木理論 → Sasaki Theory
DeepSeekTranslationService: Name mapping - 忌中 → Mourning
DeepSeekTranslationService: Name mapping - 練炭 → Charcoal briquette
DeepSeekTranslationService: Name mapping - お茶 → Tea
DeepSeekTranslationService: Name mapping - 杉玉 → Sugidama (Cedar Ball  (type of charm))
DeepSeekTranslationService: Name mapping - お守り → Omamori (Amulet / Charm)
DeepSeekTranslationService: Name mapping - 卵形の鈴 → Egg‑shaped bell
DeepSeekTranslationService: Name mapping - お札 → Ofuda (Talisman / Charm)
DeepSeekTranslationService: Name mapping - カエデ → Maple
DeepSeekTranslationService: Name mapping - 桜 → Sakura
DeepSeekTranslationService: Name mapping - クロワッサン → Croissant
DeepSeekTranslationService: Name mapping - クリームパン → Cream Bun
DeepSeekTranslationService: Name mapping - チョココロネ → Chocolate Coronet
DeepSeekTranslationService: Name mapping - カフェオレ → Cafe au Lait
DeepSeekTranslationService: Name mapping - 桜の木の玉 → Sakura no ki no tama
DeepSeekTranslationService: Name mapping - 船 → Ship
DeepSeekTranslationService: Name mapping - 木の鈴 → Wooden Bell
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: 37 Release and Croissant
DeepSeekTranslationService: Cleaned title result: 37 Release and Croissant
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation, Significant punctuation loss detected
TranslationService: Original title response: 37 Release and Croissant
TranslationService: Cleaned title result: 37 Release and Croissant
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 46 entries
DeepSeekTranslationService: Name mapping - レイ → Rei
DeepSeekTranslationService: Name mapping - 近藤哲也 → Tetsuya Kondo
DeepSeekTranslationService: Name mapping - 夏川レイ → Rei Natsukawa
DeepSeekTranslationService: Name mapping - 後藤 → Goto
DeepSeekTranslationService: Name mapping - アキラ → Akira
DeepSeekTranslationService: Name mapping - 佐々木さん → Sasaki-san
DeepSeekTranslationService: Name mapping - 斎藤君 → Saito-kun
DeepSeekTranslationService: Name mapping - レイちゃん → Rei-chan
DeepSeekTranslationService: Name mapping - お爺ちゃん → Ojichan
DeepSeekTranslationService: Name mapping - 加藤 → Kato
DeepSeekTranslationService: Name mapping - 中田 → Nakata
DeepSeekTranslationService: Name mapping - 斎藤 → Saito
DeepSeekTranslationService: Name mapping - 接客室 → Reception Room
DeepSeekTranslationService: Name mapping - 新宿 → Shinjuku
DeepSeekTranslationService: Name mapping - 事務所 → Office
DeepSeekTranslationService: Name mapping - 池袋 → Ikebukuro
DeepSeekTranslationService: Name mapping - 喫茶店 → Café
DeepSeekTranslationService: Name mapping - 東京駅 → Tokyo Station
DeepSeekTranslationService: Name mapping - キカワ工業 → Kikawa Industries
DeepSeekTranslationService: Name mapping - スナック由紀子 → Snack Yukiko
DeepSeekTranslationService: Name mapping - ＪＲ → JR (Japan Railways)
DeepSeekTranslationService: Name mapping - フランス → France
DeepSeekTranslationService: Name mapping - 血管拡張 → Blood Vessel Expansion
DeepSeekTranslationService: Name mapping - 記憶阻害 → Memory Inhibition
DeepSeekTranslationService: Name mapping - 解毒 → Detoxification
DeepSeekTranslationService: Name mapping - 治癒 → Healing
DeepSeekTranslationService: Name mapping - 火魔法 → Fire Magic
DeepSeekTranslationService: Name mapping - 治癒魔法 → Healing Magic
DeepSeekTranslationService: Name mapping - 魔力 → Magic Power
DeepSeekTranslationService: Name mapping - 佐々木理論 → Sasaki Theory
DeepSeekTranslationService: Name mapping - 忌中 → Mourning
DeepSeekTranslationService: Name mapping - 練炭 → Charcoal briquette
DeepSeekTranslationService: Name mapping - お茶 → Tea
DeepSeekTranslationService: Name mapping - 杉玉 → Sugidama (Cedar Ball  (type of charm))
DeepSeekTranslationService: Name mapping - お守り → Omamori (Amulet / Charm)
DeepSeekTranslationService: Name mapping - 卵形の鈴 → Egg‑shaped bell
DeepSeekTranslationService: Name mapping - お札 → Ofuda (Talisman / Charm)
DeepSeekTranslationService: Name mapping - カエデ → Maple
DeepSeekTranslationService: Name mapping - 桜 → Sakura
DeepSeekTranslationService: Name mapping - クロワッサン → Croissant
DeepSeekTranslationService: Name mapping - クリームパン → Cream Bun
DeepSeekTranslationService: Name mapping - チョココロネ → Chocolate Coronet
DeepSeekTranslationService: Name mapping - カフェオレ → Cafe au Lait
DeepSeekTranslationService: Name mapping - 桜の木の玉 → Sakura no ki no tama
DeepSeekTranslationService: Name mapping - 船 → Ship
DeepSeekTranslationService: Name mapping - 木の鈴 → Wooden Bell
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 「 at position 241
DeepSeekTranslationService: Restored " → 」 at position 405
DeepSeekTranslationService: Restored ( → （ at position 684
DeepSeekTranslationService: Restored ) → ） at position 730
DeepSeekTranslationService: Restored " → 「 at position 1132
DeepSeekTranslationService: Restored " → 」 at position 1247
DeepSeekTranslationService: Restored " → 「 at position 1250
DeepSeekTranslationService: Restored " → 」 at position 1274
DeepSeekTranslationService: Restored " → 「 at position 1277
DeepSeekTranslationService: Restored " → 『 at position 1647
DeepSeekTranslationService: Restored " → 』 at position 1650
DeepSeekTranslationService: Restored " → 」 at position 1687
DeepSeekTranslationService: Restored " → 「 at position 1690
DeepSeekTranslationService: Restored " → 」 at position 1742
DeepSeekTranslationService: Restored " → 「 at position 1745
DeepSeekTranslationService: Restored " → 」 at position 1791
DeepSeekTranslationService: Restored " → 「 at position 2443
DeepSeekTranslationService: Restored " → 」 at position 2724
DeepSeekTranslationService: Restored ( → （ at position 1810
DeepSeekTranslationService: Restored ) → ） at position 1854
DeepSeekTranslationService: Restored " → 『 at position 2852
DeepSeekTranslationService: Restored " → 』 at position 2902
DeepSeekTranslationService: Restored " → 『 at position 3140
DeepSeekTranslationService: Restored " → 』 at position 3148
DeepSeekTranslationService: Restored ( → （ at position 2244
DeepSeekTranslationService: Restored ) → ） at position 2259
DeepSeekTranslationService: Restored " → 「 at position 3312
DeepSeekTranslationService: Restored " → 」 at position 3321
DeepSeekTranslationService: Restored " → 「 at position 3962
DeepSeekTranslationService: Restored " → 」 at position 3998
DeepSeekTranslationService: Restored " → 「 at position 4001
DeepSeekTranslationService: Restored " → 」 at position 4027
DeepSeekTranslationService: Restored " → 「 at position 4318
DeepSeekTranslationService: Restored " → 」 at position 4396
DeepSeekTranslationService: Restored " → 「 at position 4711
DeepSeekTranslationService: Restored " → 」 at position 4796
DeepSeekTranslationService: Restored " → 「 at position 4833
DeepSeekTranslationService: Restored " → 」 at position 4870
DeepSeekTranslationService: Restored " → 「 at position 5001
DeepSeekTranslationService: Restored " → 」 at position 5016
DeepSeekTranslationService: Restored " → 「 at position 5411
DeepSeekTranslationService: Restored " → 」 at position 5425
DeepSeekTranslationService: Restored " → 「 at position 5428
DeepSeekTranslationService: Restored " → 」 at position 5453
DeepSeekTranslationService: Restored " → 「 at position 5456
DeepSeekTranslationService: Restored " → 」 at position 5498
DeepSeekTranslationService: Restored ( → （ at position 5100
DeepSeekTranslationService: Restored ) → ） at position 5132
DeepSeekTranslationService: Restored " → 「 at position 5501
DeepSeekTranslationService: Restored " → 」 at position 5593
DeepSeekTranslationService: Restored " → 「 at position 5596
DeepSeekTranslationService: Restored " → 」 at position 5622
DeepSeekTranslationService: Restored " → 「 at position 6100
DeepSeekTranslationService: Restored " → 」 at position 6109
DeepSeekTranslationService: Restored " → 「 at position 6132
DeepSeekTranslationService: Restored " → 」 at position 6159
DeepSeekTranslationService: Restored " → 「 at position 6232
DeepSeekTranslationService: Restored " → 」 at position 6288
DeepSeekTranslationService: Restored ( → （ at position 5625
DeepSeekTranslationService: Restored ) → ） at position 5729
DeepSeekTranslationService: Restored " → 「 at position 6291
DeepSeekTranslationService: Restored " → 」 at position 6339
DeepSeekTranslationService: Restored " → 「 at position 6342
DeepSeekTranslationService: Restored " → 」 at position 6388
DeepSeekTranslationService: Restored " → 「 at position 6434
DeepSeekTranslationService: Restored " → 」 at position 6620
DeepSeekTranslationService: Restored " → 「 at position 6623
DeepSeekTranslationService: Restored " → 」 at position 6665
DeepSeekTranslationService: Restored " → 「 at position 6833
DeepSeekTranslationService: Restored " → 」 at position 6977
DeepSeekTranslationService: Restored ( → （ at position 6391
DeepSeekTranslationService: Restored ) → ） at position 6431
DeepSeekTranslationService: Restored " → 「 at position 7124
DeepSeekTranslationService: Restored " → 」 at position 7177
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 8285
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: 'アキラ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'スナック由紀子' (matched pattern: /^[。！？、，]/)
TranslationService: AI parsed 3 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Selective filtering reduced pattern names from 10 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":37,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":2,"chapter_number":38,"target_language":"en"}
Starting translate - Novel ID: 2, Chapters: 38, Language: en
TranslationService: Starting chapter translation - Novel ID: 2, Chapter ID: 314
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Processing furigana content
TranslationService: Chapter found - Title: 38　因果とは
TranslationService: Chapter content length: 6337
TranslationService: getNameDictionary query returned 46 entries for novel 2
TranslationService: Name dictionary entries: 46
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 46 entries
DeepSeekTranslationService: Name mapping - レイ → Rei
DeepSeekTranslationService: Name mapping - 近藤哲也 → Tetsuya Kondo
DeepSeekTranslationService: Name mapping - 夏川レイ → Rei Natsukawa
DeepSeekTranslationService: Name mapping - 後藤 → Goto
DeepSeekTranslationService: Name mapping - アキラ → Akira
DeepSeekTranslationService: Name mapping - 佐々木さん → Sasaki-san
DeepSeekTranslationService: Name mapping - 斎藤君 → Saito-kun
DeepSeekTranslationService: Name mapping - レイちゃん → Rei-chan
DeepSeekTranslationService: Name mapping - お爺ちゃん → Ojichan
DeepSeekTranslationService: Name mapping - 加藤 → Kato
DeepSeekTranslationService: Name mapping - 中田 → Nakata
DeepSeekTranslationService: Name mapping - 斎藤 → Saito
DeepSeekTranslationService: Name mapping - 接客室 → Reception Room
DeepSeekTranslationService: Name mapping - 新宿 → Shinjuku
DeepSeekTranslationService: Name mapping - 事務所 → Office
DeepSeekTranslationService: Name mapping - 池袋 → Ikebukuro
DeepSeekTranslationService: Name mapping - 喫茶店 → Café
DeepSeekTranslationService: Name mapping - 東京駅 → Tokyo Station
DeepSeekTranslationService: Name mapping - キカワ工業 → Kikawa Industries
DeepSeekTranslationService: Name mapping - スナック由紀子 → Snack Yukiko
DeepSeekTranslationService: Name mapping - ＪＲ → JR (Japan Railways)
DeepSeekTranslationService: Name mapping - フランス → France
DeepSeekTranslationService: Name mapping - 血管拡張 → Blood Vessel Expansion
DeepSeekTranslationService: Name mapping - 記憶阻害 → Memory Inhibition
DeepSeekTranslationService: Name mapping - 解毒 → Detoxification
DeepSeekTranslationService: Name mapping - 治癒 → Healing
DeepSeekTranslationService: Name mapping - 火魔法 → Fire Magic
DeepSeekTranslationService: Name mapping - 治癒魔法 → Healing Magic
DeepSeekTranslationService: Name mapping - 魔力 → Magic Power
DeepSeekTranslationService: Name mapping - 佐々木理論 → Sasaki Theory
DeepSeekTranslationService: Name mapping - 忌中 → Mourning
DeepSeekTranslationService: Name mapping - 練炭 → Charcoal briquette
DeepSeekTranslationService: Name mapping - お茶 → Tea
DeepSeekTranslationService: Name mapping - 杉玉 → Sugidama (Cedar Ball  (type of charm))
DeepSeekTranslationService: Name mapping - お守り → Omamori (Amulet / Charm)
DeepSeekTranslationService: Name mapping - 卵形の鈴 → Egg‑shaped bell
DeepSeekTranslationService: Name mapping - お札 → Ofuda (Talisman / Charm)
DeepSeekTranslationService: Name mapping - カエデ → Maple
DeepSeekTranslationService: Name mapping - 桜 → Sakura
DeepSeekTranslationService: Name mapping - クロワッサン → Croissant
DeepSeekTranslationService: Name mapping - クリームパン → Cream Bun
DeepSeekTranslationService: Name mapping - チョココロネ → Chocolate Coronet
DeepSeekTranslationService: Name mapping - カフェオレ → Cafe au Lait
DeepSeekTranslationService: Name mapping - 桜の木の玉 → Sakura no ki no tama
DeepSeekTranslationService: Name mapping - 船 → Ship
DeepSeekTranslationService: Name mapping - 木の鈴 → Wooden Bell
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: 38 Karma
DeepSeekTranslationService: Cleaned title result: 38 Karma
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation, Significant punctuation loss detected
TranslationService: Original title response: 38 Karma
TranslationService: Cleaned title result: 38 Karma
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 46 entries
DeepSeekTranslationService: Name mapping - レイ → Rei
DeepSeekTranslationService: Name mapping - 近藤哲也 → Tetsuya Kondo
DeepSeekTranslationService: Name mapping - 夏川レイ → Rei Natsukawa
DeepSeekTranslationService: Name mapping - 後藤 → Goto
DeepSeekTranslationService: Name mapping - アキラ → Akira
DeepSeekTranslationService: Name mapping - 佐々木さん → Sasaki-san
DeepSeekTranslationService: Name mapping - 斎藤君 → Saito-kun
DeepSeekTranslationService: Name mapping - レイちゃん → Rei-chan
DeepSeekTranslationService: Name mapping - お爺ちゃん → Ojichan
DeepSeekTranslationService: Name mapping - 加藤 → Kato
DeepSeekTranslationService: Name mapping - 中田 → Nakata
DeepSeekTranslationService: Name mapping - 斎藤 → Saito
DeepSeekTranslationService: Name mapping - 接客室 → Reception Room
DeepSeekTranslationService: Name mapping - 新宿 → Shinjuku
DeepSeekTranslationService: Name mapping - 事務所 → Office
DeepSeekTranslationService: Name mapping - 池袋 → Ikebukuro
DeepSeekTranslationService: Name mapping - 喫茶店 → Café
DeepSeekTranslationService: Name mapping - 東京駅 → Tokyo Station
DeepSeekTranslationService: Name mapping - キカワ工業 → Kikawa Industries
DeepSeekTranslationService: Name mapping - スナック由紀子 → Snack Yukiko
DeepSeekTranslationService: Name mapping - ＪＲ → JR (Japan Railways)
DeepSeekTranslationService: Name mapping - フランス → France
DeepSeekTranslationService: Name mapping - 血管拡張 → Blood Vessel Expansion
DeepSeekTranslationService: Name mapping - 記憶阻害 → Memory Inhibition
DeepSeekTranslationService: Name mapping - 解毒 → Detoxification
DeepSeekTranslationService: Name mapping - 治癒 → Healing
DeepSeekTranslationService: Name mapping - 火魔法 → Fire Magic
DeepSeekTranslationService: Name mapping - 治癒魔法 → Healing Magic
DeepSeekTranslationService: Name mapping - 魔力 → Magic Power
DeepSeekTranslationService: Name mapping - 佐々木理論 → Sasaki Theory
DeepSeekTranslationService: Name mapping - 忌中 → Mourning
DeepSeekTranslationService: Name mapping - 練炭 → Charcoal briquette
DeepSeekTranslationService: Name mapping - お茶 → Tea
DeepSeekTranslationService: Name mapping - 杉玉 → Sugidama (Cedar Ball  (type of charm))
DeepSeekTranslationService: Name mapping - お守り → Omamori (Amulet / Charm)
DeepSeekTranslationService: Name mapping - 卵形の鈴 → Egg‑shaped bell
DeepSeekTranslationService: Name mapping - お札 → Ofuda (Talisman / Charm)
DeepSeekTranslationService: Name mapping - カエデ → Maple
DeepSeekTranslationService: Name mapping - 桜 → Sakura
DeepSeekTranslationService: Name mapping - クロワッサン → Croissant
DeepSeekTranslationService: Name mapping - クリームパン → Cream Bun
DeepSeekTranslationService: Name mapping - チョココロネ → Chocolate Coronet
DeepSeekTranslationService: Name mapping - カフェオレ → Cafe au Lait
DeepSeekTranslationService: Name mapping - 桜の木の玉 → Sakura no ki no tama
DeepSeekTranslationService: Name mapping - 船 → Ship
DeepSeekTranslationService: Name mapping - 木の鈴 → Wooden Bell
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 『 at position 527
DeepSeekTranslationService: Restored " → 』 at position 585
DeepSeekTranslationService: Restored " → 『 at position 590
DeepSeekTranslationService: Restored " → 』 at position 792
DeepSeekTranslationService: Restored " → 『 at position 797
DeepSeekTranslationService: Restored " → 』 at position 800
DeepSeekTranslationService: Restored " → 『 at position 805
DeepSeekTranslationService: Restored " → 』 at position 1083
DeepSeekTranslationService: Restored " → 「 at position 1088
DeepSeekTranslationService: Restored " → 」 at position 1091
DeepSeekTranslationService: Restored " → 「 at position 1175
DeepSeekTranslationService: Restored " → 」 at position 1251
DeepSeekTranslationService: Restored " → 「 at position 1256
DeepSeekTranslationService: Restored " → 」 at position 1357
DeepSeekTranslationService: Restored " → 「 at position 1362
DeepSeekTranslationService: Restored " → 」 at position 1433
DeepSeekTranslationService: Restored " → 「 at position 1438
DeepSeekTranslationService: Restored " → 」 at position 1519
DeepSeekTranslationService: Restored " → 「 at position 1524
DeepSeekTranslationService: Restored " → 」 at position 1611
DeepSeekTranslationService: Restored " → 「 at position 1926
DeepSeekTranslationService: Restored " → 」 at position 1962
DeepSeekTranslationService: Restored " → 「 at position 2013
DeepSeekTranslationService: Restored " → 」 at position 2016
DeepSeekTranslationService: Restored " → 「 at position 2318
DeepSeekTranslationService: Restored " → 」 at position 2635
DeepSeekTranslationService: Restored " → 「 at position 2925
DeepSeekTranslationService: Restored " → 」 at position 2966
DeepSeekTranslationService: Restored ( → （ at position 1770
DeepSeekTranslationService: Restored ) → ） at position 1816
DeepSeekTranslationService: Restored " → 「 at position 2971
DeepSeekTranslationService: Restored " → 」 at position 3047
DeepSeekTranslationService: Restored " → 「 at position 3052
DeepSeekTranslationService: Restored " → 」 at position 3075
DeepSeekTranslationService: Restored " → 「 at position 3293
DeepSeekTranslationService: Restored " → 」 at position 3305
DeepSeekTranslationService: Restored " → 「 at position 3806
DeepSeekTranslationService: Restored " → 」 at position 3917
DeepSeekTranslationService: Restored " → 「 at position 4088
DeepSeekTranslationService: Restored " → 」 at position 4100
DeepSeekTranslationService: Restored ( → （ at position 2018
DeepSeekTranslationService: Restored ) → ） at position 2035
DeepSeekTranslationService: Restored ( → （ at position 3170
DeepSeekTranslationService: Restored ) → ） at position 3214
DeepSeekTranslationService: Restored " → 「 at position 4349
DeepSeekTranslationService: Restored " → 」 at position 4366
DeepSeekTranslationService: Restored " → 「 at position 4449
DeepSeekTranslationService: Restored " → 」 at position 4467
DeepSeekTranslationService: Restored " → 「 at position 4543
DeepSeekTranslationService: Restored " → 」 at position 4691
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 6337
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: 'レイ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '校長先生' (matched pattern: /[。！？、，]$/)
TranslationService: AI parsed 3 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Selective filtering reduced pattern names from 10 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":38,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":7,"chapter_number":22,"target_language":"en"}
Starting translate - Novel ID: 7, Chapters: 22, Language: en
TranslationService: Starting chapter translation - Novel ID: 7, Chapter ID: 1509
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Processing furigana content
TranslationService: Chapter found - Title: 第22話　復讐の闇商人
TranslationService: Chapter content length: 8859
TranslationService: getNameDictionary query returned 55 entries for novel 7
TranslationService: Name dictionary entries: 55
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 55 entries
DeepSeekTranslationService: Name mapping - アイン → Ain
DeepSeekTranslationService: Name mapping - ライク → Laik
DeepSeekTranslationService: Name mapping - 父さん → 
DeepSeekTranslationService: Name mapping - ティルティ → Tilty
DeepSeekTranslationService: Name mapping - ソルド → Soldo
DeepSeekTranslationService: Name mapping - シルリア → Shirlia
DeepSeekTranslationService: Name mapping - シルリアさん → Shirlia-san
DeepSeekTranslationService: Name mapping - アインさん → Ain-san
DeepSeekTranslationService: Name mapping - アイテム → Item
DeepSeekTranslationService: Name mapping - プレイヤー → Player
DeepSeekTranslationService: Name mapping - ラゴン → ragon
DeepSeekTranslationService: Name mapping - ドゴラ → Dogora
DeepSeekTranslationService: Name mapping - ダンジョン → Dungeon
DeepSeekTranslationService: Name mapping - 侯爵家の屋敷 → Kōshakuke no yashiki
DeepSeekTranslationService: Name mapping - 侯爵家 → Marquis Household
DeepSeekTranslationService: Name mapping - 中立的な家臣達 → Neutral Retainers
DeepSeekTranslationService: Name mapping - 奴隷商 → Slave Merchant
DeepSeekTranslationService: Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash)
DeepSeekTranslationService: Name mapping - 魔神流 → Majin Ryu (Demon God Style)
DeepSeekTranslationService: Name mapping - 魔法スキル → Magic Skill
DeepSeekTranslationService: Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship)
DeepSeekTranslationService: Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash)
DeepSeekTranslationService: Name mapping - 炎のブレス → Flame Breath
DeepSeekTranslationService: Name mapping - 《千里眼》 → Senrigan (Clairvoyance)
DeepSeekTranslationService: Name mapping - 魔法 → Magic
DeepSeekTranslationService: Name mapping - 対毒魔法 → Anti-Poison Magic
DeepSeekTranslationService: Name mapping - リカバリー → Recovery
DeepSeekTranslationService: Name mapping - 闇天太陽 → Dark Sky Sun
DeepSeekTranslationService: Name mapping - 天姫、空閃 → Sky Princess, Sky Flash
DeepSeekTranslationService: Name mapping - ブラックドラゴン → Black Dragon
DeepSeekTranslationService: Name mapping - 魔獣 → Magic Beast
DeepSeekTranslationService: Name mapping - ドラゴン → Dragon
DeepSeekTranslationService: Name mapping - ブラックドラ → Black Dragon
DeepSeekTranslationService: Name mapping - 魔獣災害 → Magic Beast Disaster
DeepSeekTranslationService: Name mapping - 魔力 → Magical Power
DeepSeekTranslationService: Name mapping - 兄さん → Nii-san
DeepSeekTranslationService: Name mapping - 妹達 → Imōto-tachi
DeepSeekTranslationService: Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded
DeepSeekTranslationService: Name mapping - ホッ → Relief (sigh of relief)
DeepSeekTranslationService: Name mapping - ゾッコン → Deeply in love / Infatuated
DeepSeekTranslationService: Name mapping - 心底ホッ → Deep Relief
DeepSeekTranslationService: Name mapping - 腹黒メガネ → Scheming Glasses-wearer
DeepSeekTranslationService: Name mapping - ダメージ → Damage
DeepSeekTranslationService: Name mapping - リスク → Risk
DeepSeekTranslationService: Name mapping - 魔人 → Demon
DeepSeekTranslationService: Name mapping - 回復アイテム → Healing Item
DeepSeekTranslationService: Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》
DeepSeekTranslationService: Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》
DeepSeekTranslationService: Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》
DeepSeekTranslationService: Name mapping - 紅蓮の炎 → Guren no honō
DeepSeekTranslationService: Name mapping - 睡眠薬 → Sleeping pill
DeepSeekTranslationService: Name mapping - 呪具 → Cursed Item
DeepSeekTranslationService: Name mapping - 剣術 → Kenjutsu (Swordsmanship)
DeepSeekTranslationService: Name mapping - 次期当主 → Next Family Head
DeepSeekTranslationService: Name mapping - メイド → Maid
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Chapter 22: The Dark Merchant of Revenge
DeepSeekTranslationService: Cleaned title result: Chapter 22: The Dark Merchant of Revenge
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation, Significant punctuation loss detected
TranslationService: Original title response: Chapter 22: The Dark Merchant of Revenge
TranslationService: Cleaned title result: Chapter 22: The Dark Merchant of Revenge
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 55 entries
DeepSeekTranslationService: Name mapping - アイン → Ain
DeepSeekTranslationService: Name mapping - ライク → Laik
DeepSeekTranslationService: Name mapping - 父さん → 
DeepSeekTranslationService: Name mapping - ティルティ → Tilty
DeepSeekTranslationService: Name mapping - ソルド → Soldo
DeepSeekTranslationService: Name mapping - シルリア → Shirlia
DeepSeekTranslationService: Name mapping - シルリアさん → Shirlia-san
DeepSeekTranslationService: Name mapping - アインさん → Ain-san
DeepSeekTranslationService: Name mapping - アイテム → Item
DeepSeekTranslationService: Name mapping - プレイヤー → Player
DeepSeekTranslationService: Name mapping - ラゴン → ragon
DeepSeekTranslationService: Name mapping - ドゴラ → Dogora
DeepSeekTranslationService: Name mapping - ダンジョン → Dungeon
DeepSeekTranslationService: Name mapping - 侯爵家の屋敷 → Kōshakuke no yashiki
DeepSeekTranslationService: Name mapping - 侯爵家 → Marquis Household
DeepSeekTranslationService: Name mapping - 中立的な家臣達 → Neutral Retainers
DeepSeekTranslationService: Name mapping - 奴隷商 → Slave Merchant
DeepSeekTranslationService: Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash)
DeepSeekTranslationService: Name mapping - 魔神流 → Majin Ryu (Demon God Style)
DeepSeekTranslationService: Name mapping - 魔法スキル → Magic Skill
DeepSeekTranslationService: Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship)
DeepSeekTranslationService: Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash)
DeepSeekTranslationService: Name mapping - 炎のブレス → Flame Breath
DeepSeekTranslationService: Name mapping - 《千里眼》 → Senrigan (Clairvoyance)
DeepSeekTranslationService: Name mapping - 魔法 → Magic
DeepSeekTranslationService: Name mapping - 対毒魔法 → Anti-Poison Magic
DeepSeekTranslationService: Name mapping - リカバリー → Recovery
DeepSeekTranslationService: Name mapping - 闇天太陽 → Dark Sky Sun
DeepSeekTranslationService: Name mapping - 天姫、空閃 → Sky Princess, Sky Flash
DeepSeekTranslationService: Name mapping - ブラックドラゴン → Black Dragon
DeepSeekTranslationService: Name mapping - 魔獣 → Magic Beast
DeepSeekTranslationService: Name mapping - ドラゴン → Dragon
DeepSeekTranslationService: Name mapping - ブラックドラ → Black Dragon
DeepSeekTranslationService: Name mapping - 魔獣災害 → Magic Beast Disaster
DeepSeekTranslationService: Name mapping - 魔力 → Magical Power
DeepSeekTranslationService: Name mapping - 兄さん → Nii-san
DeepSeekTranslationService: Name mapping - 妹達 → Imōto-tachi
DeepSeekTranslationService: Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded
DeepSeekTranslationService: Name mapping - ホッ → Relief (sigh of relief)
DeepSeekTranslationService: Name mapping - ゾッコン → Deeply in love / Infatuated
DeepSeekTranslationService: Name mapping - 心底ホッ → Deep Relief
DeepSeekTranslationService: Name mapping - 腹黒メガネ → Scheming Glasses-wearer
DeepSeekTranslationService: Name mapping - ダメージ → Damage
DeepSeekTranslationService: Name mapping - リスク → Risk
DeepSeekTranslationService: Name mapping - 魔人 → Demon
DeepSeekTranslationService: Name mapping - 回復アイテム → Healing Item
DeepSeekTranslationService: Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》
DeepSeekTranslationService: Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》
DeepSeekTranslationService: Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》
DeepSeekTranslationService: Name mapping - 紅蓮の炎 → Guren no honō
DeepSeekTranslationService: Name mapping - 睡眠薬 → Sleeping pill
DeepSeekTranslationService: Name mapping - 呪具 → Cursed Item
DeepSeekTranslationService: Name mapping - 剣術 → Kenjutsu (Swordsmanship)
DeepSeekTranslationService: Name mapping - 次期当主 → Next Family Head
DeepSeekTranslationService: Name mapping - メイド → Maid
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 「 at position 730
DeepSeekTranslationService: Restored " → 」 at position 738
DeepSeekTranslationService: Restored " → 「 at position 1052
DeepSeekTranslationService: Restored " → 」 at position 1086
DeepSeekTranslationService: Restored " → 「 at position 1089
DeepSeekTranslationService: Restored " → 」 at position 1141
DeepSeekTranslationService: Restored " → 「 at position 1144
DeepSeekTranslationService: Restored " → 」 at position 1330
DeepSeekTranslationService: Restored " → 「 at position 1588
DeepSeekTranslationService: Restored " → 」 at position 1751
DeepSeekTranslationService: Restored " → 「 at position 1754
DeepSeekTranslationService: Restored " → 」 at position 1764
DeepSeekTranslationService: Restored " → 「 at position 1823
DeepSeekTranslationService: Restored " → 」 at position 1835
DeepSeekTranslationService: Restored " → 「 at position 2084
DeepSeekTranslationService: Restored " → 」 at position 2239
DeepSeekTranslationService: Restored " → 「 at position 2242
DeepSeekTranslationService: Restored " → 」 at position 2252
DeepSeekTranslationService: Restored " → 「 at position 2403
DeepSeekTranslationService: Restored " → 」 at position 2418
DeepSeekTranslationService: Restored " → 「 at position 2608
DeepSeekTranslationService: Restored " → 」 at position 2619
DeepSeekTranslationService: Restored " → 「 at position 2622
DeepSeekTranslationService: Restored " → 」 at position 2663
DeepSeekTranslationService: Restored " → 「 at position 2863
DeepSeekTranslationService: Restored " → 」 at position 2919
DeepSeekTranslationService: Restored " → 「 at position 2922
DeepSeekTranslationService: Restored " → 」 at position 3116
DeepSeekTranslationService: Restored " → 「 at position 3119
DeepSeekTranslationService: Restored " → 」 at position 3131
DeepSeekTranslationService: Restored " → 「 at position 3464
DeepSeekTranslationService: Restored " → 」 at position 3475
DeepSeekTranslationService: Restored " → 「 at position 3621
DeepSeekTranslationService: Restored " → 」 at position 3677
DeepSeekTranslationService: Restored " → 「 at position 3680
DeepSeekTranslationService: Restored " → 」 at position 3689
DeepSeekTranslationService: Restored " → 「 at position 4094
DeepSeekTranslationService: Restored " → 」 at position 4145
DeepSeekTranslationService: Restored " → 「 at position 4361
DeepSeekTranslationService: Restored " → 」 at position 4434
DeepSeekTranslationService: Restored " → 「 at position 4756
DeepSeekTranslationService: Restored " → 」 at position 4781
DeepSeekTranslationService: Restored " → 「 at position 5142
DeepSeekTranslationService: Restored " → 」 at position 5149
DeepSeekTranslationService: Restored " → 「 at position 5278
DeepSeekTranslationService: Restored " → 」 at position 5322
DeepSeekTranslationService: Restored " → 「 at position 5465
DeepSeekTranslationService: Restored " → 」 at position 5522
DeepSeekTranslationService: Restored " → 「 at position 5525
DeepSeekTranslationService: Restored " → 」 at position 5637
DeepSeekTranslationService: Restored " → 「 at position 5640
DeepSeekTranslationService: Restored " → 」 at position 5683
DeepSeekTranslationService: Restored " → 「 at position 5686
DeepSeekTranslationService: Restored " → 」 at position 5693
DeepSeekTranslationService: Restored " → 「 at position 5810
DeepSeekTranslationService: Restored " → 」 at position 5885
DeepSeekTranslationService: Restored " → 「 at position 6022
DeepSeekTranslationService: Restored " → 」 at position 6086
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 8859
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: 'シルリア' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ティルティ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ドゴラ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '《氷狼一閃》' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '《黒竜炎斬》' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '《闇天太陽》' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '《天姫、空閃》' (matched pattern: /^[。！？、，]/)
TranslationService: AI parsed 0 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Selective filtering reduced pattern names from 10 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":22,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":7,"chapter_number":23,"target_language":"en"}
Starting translate - Novel ID: 7, Chapters: 23, Language: en
TranslationService: Starting chapter translation - Novel ID: 7, Chapter ID: 1510
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Processing furigana content
TranslationService: Chapter found - Title: 第23話　とある家臣の焦り
TranslationService: Chapter content length: 8839
TranslationService: getNameDictionary query returned 64 entries for novel 7
TranslationService: Name dictionary entries: 64
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 64 entries
DeepSeekTranslationService: Name mapping - アイン → Ain
DeepSeekTranslationService: Name mapping - ライク → Laik
DeepSeekTranslationService: Name mapping - 父さん → 
DeepSeekTranslationService: Name mapping - ティルティ → Tilty
DeepSeekTranslationService: Name mapping - ソルド → Soldo
DeepSeekTranslationService: Name mapping - シルリア → Shirlia
DeepSeekTranslationService: Name mapping - シルリアさん → Shirlia-san
DeepSeekTranslationService: Name mapping - アインさん → Ain-san
DeepSeekTranslationService: Name mapping - アイテム → Item
DeepSeekTranslationService: Name mapping - プレイヤー → Player
DeepSeekTranslationService: Name mapping - ラゴン → ragon
DeepSeekTranslationService: Name mapping - ドゴラ → Dogora
DeepSeekTranslationService: Name mapping - ワルガー → Walgar
DeepSeekTranslationService: Name mapping - シルリア・ハルトマン → Shirlia Hartmann
DeepSeekTranslationService: Name mapping - ティルティ・レンジャー → Tilty Ranger
DeepSeekTranslationService: Name mapping - ソルド・レンジャー → Soldo Ranger
DeepSeekTranslationService: Name mapping - ダンジョン → Dungeon
DeepSeekTranslationService: Name mapping - 侯爵家の屋敷 → Kōshakuke no yashiki
DeepSeekTranslationService: Name mapping - 影 → Shadow
DeepSeekTranslationService: Name mapping - タイタンズ → Titans
DeepSeekTranslationService: Name mapping - 侯爵家 → Marquis Household
DeepSeekTranslationService: Name mapping - 中立的な家臣達 → Neutral Retainers
DeepSeekTranslationService: Name mapping - 奴隷商 → Slave Merchant
DeepSeekTranslationService: Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family
DeepSeekTranslationService: Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash)
DeepSeekTranslationService: Name mapping - 魔神流 → Majin Ryu (Demon God Style)
DeepSeekTranslationService: Name mapping - 魔法スキル → Magic Skill
DeepSeekTranslationService: Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship)
DeepSeekTranslationService: Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash)
DeepSeekTranslationService: Name mapping - 炎のブレス → Flame Breath
DeepSeekTranslationService: Name mapping - 《千里眼》 → Senrigan (Clairvoyance)
DeepSeekTranslationService: Name mapping - 魔法 → Magic
DeepSeekTranslationService: Name mapping - 対毒魔法 → Anti-Poison Magic
DeepSeekTranslationService: Name mapping - リカバリー → Recovery
DeepSeekTranslationService: Name mapping - 闇天太陽 → Dark Sky Sun
DeepSeekTranslationService: Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash)
DeepSeekTranslationService: Name mapping - 千里眼 → Clairvoyance
DeepSeekTranslationService: Name mapping - ブラックドラゴン → Black Dragon
DeepSeekTranslationService: Name mapping - 魔獣 → Magic Beast
DeepSeekTranslationService: Name mapping - ドラゴン → Dragon
DeepSeekTranslationService: Name mapping - ブラックドラ → Black Dragon
DeepSeekTranslationService: Name mapping - 魔獣災害 → Magic Beast Disaster
DeepSeekTranslationService: Name mapping - 魔力 → Magical Power
DeepSeekTranslationService: Name mapping - 兄さん → Nii-san
DeepSeekTranslationService: Name mapping - 妹達 → Imōto-tachi
DeepSeekTranslationService: Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded
DeepSeekTranslationService: Name mapping - ホッ → Relief (sigh of relief)
DeepSeekTranslationService: Name mapping - ゾッコン → Deeply in love / Infatuated
DeepSeekTranslationService: Name mapping - 心底ホッ → Deep Relief
DeepSeekTranslationService: Name mapping - 腹黒メガネ → Scheming Glasses-wearer
DeepSeekTranslationService: Name mapping - ダメージ → Damage
DeepSeekTranslationService: Name mapping - リスク → Risk
DeepSeekTranslationService: Name mapping - 魔人 → Demon
DeepSeekTranslationService: Name mapping - 魔道具 → Magic Tool
DeepSeekTranslationService: Name mapping - 回復アイテム → Healing Item
DeepSeekTranslationService: Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》
DeepSeekTranslationService: Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》
DeepSeekTranslationService: Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》
DeepSeekTranslationService: Name mapping - 紅蓮の炎 → Guren no honō
DeepSeekTranslationService: Name mapping - 睡眠薬 → Sleeping pill
DeepSeekTranslationService: Name mapping - 呪具 → Cursed Item
DeepSeekTranslationService: Name mapping - 剣術 → Kenjutsu (Swordsmanship)
DeepSeekTranslationService: Name mapping - 次期当主 → Next Family Head
DeepSeekTranslationService: Name mapping - メイド → Maid
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Episode 23: A Retainer's Impatience
DeepSeekTranslationService: Cleaned title result: Episode 23: A Retainer's Impatience
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Original title response: Episode 23: A Retainer's Impatience
TranslationService: Cleaned title result: Episode 23: A Retainer's Impatience
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 64 entries
DeepSeekTranslationService: Name mapping - アイン → Ain
DeepSeekTranslationService: Name mapping - ライク → Laik
DeepSeekTranslationService: Name mapping - 父さん → 
DeepSeekTranslationService: Name mapping - ティルティ → Tilty
DeepSeekTranslationService: Name mapping - ソルド → Soldo
DeepSeekTranslationService: Name mapping - シルリア → Shirlia
DeepSeekTranslationService: Name mapping - シルリアさん → Shirlia-san
DeepSeekTranslationService: Name mapping - アインさん → Ain-san
DeepSeekTranslationService: Name mapping - アイテム → Item
DeepSeekTranslationService: Name mapping - プレイヤー → Player
DeepSeekTranslationService: Name mapping - ラゴン → ragon
DeepSeekTranslationService: Name mapping - ドゴラ → Dogora
DeepSeekTranslationService: Name mapping - ワルガー → Walgar
DeepSeekTranslationService: Name mapping - シルリア・ハルトマン → Shirlia Hartmann
DeepSeekTranslationService: Name mapping - ティルティ・レンジャー → Tilty Ranger
DeepSeekTranslationService: Name mapping - ソルド・レンジャー → Soldo Ranger
DeepSeekTranslationService: Name mapping - ダンジョン → Dungeon
DeepSeekTranslationService: Name mapping - 侯爵家の屋敷 → Kōshakuke no yashiki
DeepSeekTranslationService: Name mapping - 影 → Shadow
DeepSeekTranslationService: Name mapping - タイタンズ → Titans
DeepSeekTranslationService: Name mapping - 侯爵家 → Marquis Household
DeepSeekTranslationService: Name mapping - 中立的な家臣達 → Neutral Retainers
DeepSeekTranslationService: Name mapping - 奴隷商 → Slave Merchant
DeepSeekTranslationService: Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family
DeepSeekTranslationService: Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash)
DeepSeekTranslationService: Name mapping - 魔神流 → Majin Ryu (Demon God Style)
DeepSeekTranslationService: Name mapping - 魔法スキル → Magic Skill
DeepSeekTranslationService: Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship)
DeepSeekTranslationService: Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash)
DeepSeekTranslationService: Name mapping - 炎のブレス → Flame Breath
DeepSeekTranslationService: Name mapping - 《千里眼》 → Senrigan (Clairvoyance)
DeepSeekTranslationService: Name mapping - 魔法 → Magic
DeepSeekTranslationService: Name mapping - 対毒魔法 → Anti-Poison Magic
DeepSeekTranslationService: Name mapping - リカバリー → Recovery
DeepSeekTranslationService: Name mapping - 闇天太陽 → Dark Sky Sun
DeepSeekTranslationService: Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash)
DeepSeekTranslationService: Name mapping - 千里眼 → Clairvoyance
DeepSeekTranslationService: Name mapping - ブラックドラゴン → Black Dragon
DeepSeekTranslationService: Name mapping - 魔獣 → Magic Beast
DeepSeekTranslationService: Name mapping - ドラゴン → Dragon
DeepSeekTranslationService: Name mapping - ブラックドラ → Black Dragon
DeepSeekTranslationService: Name mapping - 魔獣災害 → Magic Beast Disaster
DeepSeekTranslationService: Name mapping - 魔力 → Magical Power
DeepSeekTranslationService: Name mapping - 兄さん → Nii-san
DeepSeekTranslationService: Name mapping - 妹達 → Imōto-tachi
DeepSeekTranslationService: Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded
DeepSeekTranslationService: Name mapping - ホッ → Relief (sigh of relief)
DeepSeekTranslationService: Name mapping - ゾッコン → Deeply in love / Infatuated
DeepSeekTranslationService: Name mapping - 心底ホッ → Deep Relief
DeepSeekTranslationService: Name mapping - 腹黒メガネ → Scheming Glasses-wearer
DeepSeekTranslationService: Name mapping - ダメージ → Damage
DeepSeekTranslationService: Name mapping - リスク → Risk
DeepSeekTranslationService: Name mapping - 魔人 → Demon
DeepSeekTranslationService: Name mapping - 魔道具 → Magic Tool
DeepSeekTranslationService: Name mapping - 回復アイテム → Healing Item
DeepSeekTranslationService: Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》
DeepSeekTranslationService: Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》
DeepSeekTranslationService: Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》
DeepSeekTranslationService: Name mapping - 紅蓮の炎 → Guren no honō
DeepSeekTranslationService: Name mapping - 睡眠薬 → Sleeping pill
DeepSeekTranslationService: Name mapping - 呪具 → Cursed Item
DeepSeekTranslationService: Name mapping - 剣術 → Kenjutsu (Swordsmanship)
DeepSeekTranslationService: Name mapping - 次期当主 → Next Family Head
DeepSeekTranslationService: Name mapping - メイド → Maid
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 「 at position 0
DeepSeekTranslationService: Restored " → 」 at position 59
DeepSeekTranslationService: Restored " → 「 at position 418
DeepSeekTranslationService: Restored " → 」 at position 593
DeepSeekTranslationService: Restored " → 「 at position 596
DeepSeekTranslationService: Restored " → 」 at position 702
DeepSeekTranslationService: Restored " → 「 at position 705
DeepSeekTranslationService: Restored " → 」 at position 723
DeepSeekTranslationService: Restored " → 「 at position 1775
DeepSeekTranslationService: Restored " → 」 at position 1918
DeepSeekTranslationService: Restored " → 「 at position 1926
DeepSeekTranslationService: Restored " → 」 at position 2035
DeepSeekTranslationService: Restored " → 「 at position 2556
DeepSeekTranslationService: Restored " → 」 at position 2787
DeepSeekTranslationService: Restored " → 「 at position 2793
DeepSeekTranslationService: Restored " → 」 at position 2832
DeepSeekTranslationService: Restored " → 「 at position 2835
DeepSeekTranslationService: Restored " → 」 at position 2850
DeepSeekTranslationService: Restored " → 「 at position 3142
DeepSeekTranslationService: Restored " → 」 at position 3228
DeepSeekTranslationService: Restored " → 「 at position 3389
DeepSeekTranslationService: Restored " → 」 at position 3394
DeepSeekTranslationService: Restored " → 「 at position 3408
DeepSeekTranslationService: Restored " → 」 at position 3413
DeepSeekTranslationService: Restored " → 「 at position 3697
DeepSeekTranslationService: Restored " → 」 at position 3702
DeepSeekTranslationService: Restored " → 「 at position 3817
DeepSeekTranslationService: Restored " → 」 at position 3859
DeepSeekTranslationService: Restored " → 「 at position 3948
DeepSeekTranslationService: Restored " → 」 at position 3958
DeepSeekTranslationService: Restored " → 「 at position 4166
DeepSeekTranslationService: Restored " → 」 at position 4219
DeepSeekTranslationService: Restored " → 「 at position 4267
DeepSeekTranslationService: Restored " → 」 at position 4309
DeepSeekTranslationService: Restored " → 「 at position 4650
DeepSeekTranslationService: Restored " → 」 at position 4782
DeepSeekTranslationService: Restored " → 「 at position 4785
DeepSeekTranslationService: Restored " → 」 at position 4820
DeepSeekTranslationService: Restored " → 「 at position 4823
DeepSeekTranslationService: Restored " → 」 at position 4852
DeepSeekTranslationService: Restored " → 「 at position 5048
DeepSeekTranslationService: Restored " → 」 at position 5071
DeepSeekTranslationService: Restored " → 「 at position 5074
DeepSeekTranslationService: Restored " → 」 at position 5259
DeepSeekTranslationService: Restored " → 「 at position 5262
DeepSeekTranslationService: Restored " → 」 at position 5271
DeepSeekTranslationService: Restored " → 「 at position 5519
DeepSeekTranslationService: Restored " → 」 at position 5659
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 8839
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: 'ワルガー' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'シルリア・ハルトマン' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ティルティ・レンジャー' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ソルド・レンジャー' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ライク' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ハルトマン侯爵家' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'タイタンズ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '《千里眼》' (matched pattern: /^[。！？、，]/)
TranslationService: AI parsed 0 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Selective filtering reduced pattern names from 10 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":23,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":7,"chapter_number":23,"target_language":"en"}
Starting translate - Novel ID: 7, Chapters: 23, Language: en
TranslationService: Starting chapter translation - Novel ID: 7, Chapter ID: 1510
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Processing furigana content
TranslationService: Chapter found - Title: 第23話　とある家臣の焦り
TranslationService: Chapter content length: 8839
TranslationService: getNameDictionary query returned 64 entries for novel 7
TranslationService: Name dictionary entries: 64
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 64 entries
DeepSeekTranslationService: Name mapping - アイン → Ain
DeepSeekTranslationService: Name mapping - ライク → Laik
DeepSeekTranslationService: Name mapping - 父さん → 
DeepSeekTranslationService: Name mapping - ティルティ → Tilty
DeepSeekTranslationService: Name mapping - ソルド → Soldo
DeepSeekTranslationService: Name mapping - シルリア → Shirlia
DeepSeekTranslationService: Name mapping - シルリアさん → Shirlia-san
DeepSeekTranslationService: Name mapping - アインさん → Ain-san
DeepSeekTranslationService: Name mapping - アイテム → Item
DeepSeekTranslationService: Name mapping - プレイヤー → Player
DeepSeekTranslationService: Name mapping - ラゴン → ragon
DeepSeekTranslationService: Name mapping - ドゴラ → Dogora
DeepSeekTranslationService: Name mapping - ワルガー → Walgar
DeepSeekTranslationService: Name mapping - シルリア・ハルトマン → Shirlia Hartmann
DeepSeekTranslationService: Name mapping - ティルティ・レンジャー → Tilty Ranger
DeepSeekTranslationService: Name mapping - ソルド・レンジャー → Soldo Ranger
DeepSeekTranslationService: Name mapping - ダンジョン → Dungeon
DeepSeekTranslationService: Name mapping - 侯爵家の屋敷 → Kōshakuke no yashiki
DeepSeekTranslationService: Name mapping - 影 → Shadow
DeepSeekTranslationService: Name mapping - タイタンズ → Titans
DeepSeekTranslationService: Name mapping - 侯爵家 → Marquis Household
DeepSeekTranslationService: Name mapping - 中立的な家臣達 → Neutral Retainers
DeepSeekTranslationService: Name mapping - 奴隷商 → Slave Merchant
DeepSeekTranslationService: Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family
DeepSeekTranslationService: Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash)
DeepSeekTranslationService: Name mapping - 魔神流 → Majin Ryu (Demon God Style)
DeepSeekTranslationService: Name mapping - 魔法スキル → Magic Skill
DeepSeekTranslationService: Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship)
DeepSeekTranslationService: Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash)
DeepSeekTranslationService: Name mapping - 炎のブレス → Flame Breath
DeepSeekTranslationService: Name mapping - 《千里眼》 → Senrigan (Clairvoyance)
DeepSeekTranslationService: Name mapping - 魔法 → Magic
DeepSeekTranslationService: Name mapping - 対毒魔法 → Anti-Poison Magic
DeepSeekTranslationService: Name mapping - リカバリー → Recovery
DeepSeekTranslationService: Name mapping - 闇天太陽 → Dark Sky Sun
DeepSeekTranslationService: Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash)
DeepSeekTranslationService: Name mapping - 千里眼 → Clairvoyance
DeepSeekTranslationService: Name mapping - ブラックドラゴン → Black Dragon
DeepSeekTranslationService: Name mapping - 魔獣 → Magic Beast
DeepSeekTranslationService: Name mapping - ドラゴン → Dragon
DeepSeekTranslationService: Name mapping - ブラックドラ → Black Dragon
DeepSeekTranslationService: Name mapping - 魔獣災害 → Magic Beast Disaster
DeepSeekTranslationService: Name mapping - 魔力 → Magical Power
DeepSeekTranslationService: Name mapping - 兄さん → Nii-san
DeepSeekTranslationService: Name mapping - 妹達 → Imōto-tachi
DeepSeekTranslationService: Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded
DeepSeekTranslationService: Name mapping - ホッ → Relief (sigh of relief)
DeepSeekTranslationService: Name mapping - ゾッコン → Deeply in love / Infatuated
DeepSeekTranslationService: Name mapping - 心底ホッ → Deep Relief
DeepSeekTranslationService: Name mapping - 腹黒メガネ → Scheming Glasses-wearer
DeepSeekTranslationService: Name mapping - ダメージ → Damage
DeepSeekTranslationService: Name mapping - リスク → Risk
DeepSeekTranslationService: Name mapping - 魔人 → Demon
DeepSeekTranslationService: Name mapping - 魔道具 → Magic Tool
DeepSeekTranslationService: Name mapping - 回復アイテム → Healing Item
DeepSeekTranslationService: Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》
DeepSeekTranslationService: Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》
DeepSeekTranslationService: Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》
DeepSeekTranslationService: Name mapping - 紅蓮の炎 → Guren no honō
DeepSeekTranslationService: Name mapping - 睡眠薬 → Sleeping pill
DeepSeekTranslationService: Name mapping - 呪具 → Cursed Item
DeepSeekTranslationService: Name mapping - 剣術 → Kenjutsu (Swordsmanship)
DeepSeekTranslationService: Name mapping - 次期当主 → Next Family Head
DeepSeekTranslationService: Name mapping - メイド → Maid
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Episode 23: A Retainer's Impatience
DeepSeekTranslationService: Cleaned title result: Episode 23: A Retainer's Impatience
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Original title response: Episode 23: A Retainer's Impatience
TranslationService: Cleaned title result: Episode 23: A Retainer's Impatience
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 64 entries
DeepSeekTranslationService: Name mapping - アイン → Ain
DeepSeekTranslationService: Name mapping - ライク → Laik
DeepSeekTranslationService: Name mapping - 父さん → 
DeepSeekTranslationService: Name mapping - ティルティ → Tilty
DeepSeekTranslationService: Name mapping - ソルド → Soldo
DeepSeekTranslationService: Name mapping - シルリア → Shirlia
DeepSeekTranslationService: Name mapping - シルリアさん → Shirlia-san
DeepSeekTranslationService: Name mapping - アインさん → Ain-san
DeepSeekTranslationService: Name mapping - アイテム → Item
DeepSeekTranslationService: Name mapping - プレイヤー → Player
DeepSeekTranslationService: Name mapping - ラゴン → ragon
DeepSeekTranslationService: Name mapping - ドゴラ → Dogora
DeepSeekTranslationService: Name mapping - ワルガー → Walgar
DeepSeekTranslationService: Name mapping - シルリア・ハルトマン → Shirlia Hartmann
DeepSeekTranslationService: Name mapping - ティルティ・レンジャー → Tilty Ranger
DeepSeekTranslationService: Name mapping - ソルド・レンジャー → Soldo Ranger
DeepSeekTranslationService: Name mapping - ダンジョン → Dungeon
DeepSeekTranslationService: Name mapping - 侯爵家の屋敷 → Kōshakuke no yashiki
DeepSeekTranslationService: Name mapping - 影 → Shadow
DeepSeekTranslationService: Name mapping - タイタンズ → Titans
DeepSeekTranslationService: Name mapping - 侯爵家 → Marquis Household
DeepSeekTranslationService: Name mapping - 中立的な家臣達 → Neutral Retainers
DeepSeekTranslationService: Name mapping - 奴隷商 → Slave Merchant
DeepSeekTranslationService: Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family
DeepSeekTranslationService: Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash)
DeepSeekTranslationService: Name mapping - 魔神流 → Majin Ryu (Demon God Style)
DeepSeekTranslationService: Name mapping - 魔法スキル → Magic Skill
DeepSeekTranslationService: Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship)
DeepSeekTranslationService: Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash)
DeepSeekTranslationService: Name mapping - 炎のブレス → Flame Breath
DeepSeekTranslationService: Name mapping - 《千里眼》 → Senrigan (Clairvoyance)
DeepSeekTranslationService: Name mapping - 魔法 → Magic
DeepSeekTranslationService: Name mapping - 対毒魔法 → Anti-Poison Magic
DeepSeekTranslationService: Name mapping - リカバリー → Recovery
DeepSeekTranslationService: Name mapping - 闇天太陽 → Dark Sky Sun
DeepSeekTranslationService: Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash)
DeepSeekTranslationService: Name mapping - 千里眼 → Clairvoyance
DeepSeekTranslationService: Name mapping - ブラックドラゴン → Black Dragon
DeepSeekTranslationService: Name mapping - 魔獣 → Magic Beast
DeepSeekTranslationService: Name mapping - ドラゴン → Dragon
DeepSeekTranslationService: Name mapping - ブラックドラ → Black Dragon
DeepSeekTranslationService: Name mapping - 魔獣災害 → Magic Beast Disaster
DeepSeekTranslationService: Name mapping - 魔力 → Magical Power
DeepSeekTranslationService: Name mapping - 兄さん → Nii-san
DeepSeekTranslationService: Name mapping - 妹達 → Imōto-tachi
DeepSeekTranslationService: Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded
DeepSeekTranslationService: Name mapping - ホッ → Relief (sigh of relief)
DeepSeekTranslationService: Name mapping - ゾッコン → Deeply in love / Infatuated
DeepSeekTranslationService: Name mapping - 心底ホッ → Deep Relief
DeepSeekTranslationService: Name mapping - 腹黒メガネ → Scheming Glasses-wearer
DeepSeekTranslationService: Name mapping - ダメージ → Damage
DeepSeekTranslationService: Name mapping - リスク → Risk
DeepSeekTranslationService: Name mapping - 魔人 → Demon
DeepSeekTranslationService: Name mapping - 魔道具 → Magic Tool
DeepSeekTranslationService: Name mapping - 回復アイテム → Healing Item
DeepSeekTranslationService: Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》
DeepSeekTranslationService: Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》
DeepSeekTranslationService: Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》
DeepSeekTranslationService: Name mapping - 紅蓮の炎 → Guren no honō
DeepSeekTranslationService: Name mapping - 睡眠薬 → Sleeping pill
DeepSeekTranslationService: Name mapping - 呪具 → Cursed Item
DeepSeekTranslationService: Name mapping - 剣術 → Kenjutsu (Swordsmanship)
DeepSeekTranslationService: Name mapping - 次期当主 → Next Family Head
DeepSeekTranslationService: Name mapping - メイド → Maid
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 「 at position 0
DeepSeekTranslationService: Restored " → 」 at position 51
DeepSeekTranslationService: Restored " → 「 at position 400
DeepSeekTranslationService: Restored " → 」 at position 570
DeepSeekTranslationService: Restored " → 「 at position 573
DeepSeekTranslationService: Restored " → 」 at position 701
DeepSeekTranslationService: Restored " → 「 at position 704
DeepSeekTranslationService: Restored " → 」 at position 722
DeepSeekTranslationService: Restored " → 「 at position 1816
DeepSeekTranslationService: Restored " → 」 at position 2035
DeepSeekTranslationService: Restored " → 「 at position 2583
DeepSeekTranslationService: Restored " → 」 at position 2857
DeepSeekTranslationService: Restored " → 「 at position 2860
DeepSeekTranslationService: Restored " → 」 at position 2875
DeepSeekTranslationService: Restored " → 「 at position 3159
DeepSeekTranslationService: Restored " → 」 at position 3231
DeepSeekTranslationService: Restored " → 「 at position 3412
DeepSeekTranslationService: Restored " → 」 at position 3433
DeepSeekTranslationService: Restored " → 「 at position 3902
DeepSeekTranslationService: Restored " → 」 at position 3944
DeepSeekTranslationService: Restored " → 「 at position 4239
DeepSeekTranslationService: Restored " → 」 at position 4304
DeepSeekTranslationService: Restored " → 「 at position 4363
DeepSeekTranslationService: Restored " → 」 at position 4407
DeepSeekTranslationService: Restored " → 「 at position 4802
DeepSeekTranslationService: Restored " → 」 at position 4954
DeepSeekTranslationService: Restored " → 「 at position 4957
DeepSeekTranslationService: Restored " → 」 at position 4990
DeepSeekTranslationService: Restored " → 「 at position 4993
DeepSeekTranslationService: Restored " → 」 at position 5022
DeepSeekTranslationService: Restored " → 「 at position 5268
DeepSeekTranslationService: Restored " → 」 at position 5299
DeepSeekTranslationService: Restored " → 「 at position 5302
DeepSeekTranslationService: Restored " → 」 at position 5517
DeepSeekTranslationService: Restored " → 「 at position 5520
DeepSeekTranslationService: Restored " → 」 at position 5529
DeepSeekTranslationService: Restored " → 「 at position 5760
DeepSeekTranslationService: Restored " → 」 at position 5906
DeepSeekTranslationService: Restored " → 「 at position 5909
DeepSeekTranslationService: Restored " → 」 at position 5919
DeepSeekTranslationService: Restored " → 「 at position 6163
DeepSeekTranslationService: Restored " → 」 at position 6208
DeepSeekTranslationService: Restored " → 「 at position 6211
DeepSeekTranslationService: Restored " → 」 at position 6358
DeepSeekTranslationService: Restored " → 「 at position 6361
DeepSeekTranslationService: Restored " → 」 at position 6374
DeepSeekTranslationService: Restored " → 「 at position 6558
DeepSeekTranslationService: Restored " → 」 at position 6597
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 8839
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: 'ワルガー' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'シルリア・ハルトマン' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ティルティ・レンジャー' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ソルド・レンジャー' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ライク' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ハルトマン侯爵家' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'タイタンズ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '《千里眼》' (matched pattern: /^[。！？、，]/)
TranslationService: AI parsed 0 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Selective filtering reduced pattern names from 10 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":23,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":3,"chapter_number":17,"target_language":"en"}
Starting translate - Novel ID: 3, Chapters: 17, Language: en
TranslationService: Starting chapter translation - Novel ID: 3, Chapter ID: 384
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第17話　10日目:ワイバーン墜落
TranslationService: Chapter content length: 7605
TranslationService: getNameDictionary query returned 78 entries for novel 3
TranslationService: Name dictionary entries: 78
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 78 entries
DeepSeekTranslationService: Name mapping - エルマ → Elma
DeepSeekTranslationService: Name mapping - タボ → Tabo
DeepSeekTranslationService: Name mapping - パーラハーラ3世 → Pārahāra Sansei
DeepSeekTranslationService: Name mapping - ステータス → Status
DeepSeekTranslationService: Name mapping - カバン → kaban
DeepSeekTranslationService: Name mapping - パラちゃん → Parachan
DeepSeekTranslationService: Name mapping - パパ → Papa
DeepSeekTranslationService: Name mapping - レイ → Rei
DeepSeekTranslationService: Name mapping - 世界樹林・死王の蟲野営地 → World Tree Forest・Insect King's Campsite
DeepSeekTranslationService: Name mapping - トアイラ世界樹林 → World Tree Forest
DeepSeekTranslationService: Name mapping - 死王の蟲野営地 → Dead King&#039;s Insect Encampment
DeepSeekTranslationService: Name mapping - 植物園 → Botanical Garden
DeepSeekTranslationService: Name mapping - 異世界 → Isekai
DeepSeekTranslationService: Name mapping - 冒険者ギルド → Adventurers Guild
DeepSeekTranslationService: Name mapping - エルダーフロンティア → Elder Frontiers
DeepSeekTranslationService: Name mapping - ギルド → guild
DeepSeekTranslationService: Name mapping - 一二神教 → Twelve Deities Church
DeepSeekTranslationService: Name mapping - 教会騎士団 → Church Knights
DeepSeekTranslationService: Name mapping - 毒スキル → Poison Skill
DeepSeekTranslationService: Name mapping - 薬師スキル → Pharmacist Skill
DeepSeekTranslationService: Name mapping - 毒手スキル → Poison Hand Skill
DeepSeekTranslationService: Name mapping - 外敵の察知スキル → Enemy Detection Skill
DeepSeekTranslationService: Name mapping - 毒手 → Poison Hand
DeepSeekTranslationService: Name mapping - 毒喰らい → Poison Eater
DeepSeekTranslationService: Name mapping - 毒術 → Poison Arts
DeepSeekTranslationService: Name mapping - 装 → Armament
DeepSeekTranslationService: Name mapping - 集 → Concentration
DeepSeekTranslationService: Name mapping - 変 → Transformation
DeepSeekTranslationService: Name mapping - 素材感知Ⅰ → Material Detection I
DeepSeekTranslationService: Name mapping - 外敵の察知 → Foe Detection
DeepSeekTranslationService: Name mapping - 友村友人 → Tomohito Tomomura
DeepSeekTranslationService: Name mapping - 毒喰らいⅠ → Poison Eater I
DeepSeekTranslationService: Name mapping - 毒調合Ⅰ → Poison Mixing I
DeepSeekTranslationService: Name mapping - 毒解析Ⅰ → Poison Analysis I
DeepSeekTranslationService: Name mapping - 麻痺毒Ⅰ → Paralysis Poison I
DeepSeekTranslationService: Name mapping - 毒の高揚 → Poison Excitement
DeepSeekTranslationService: Name mapping - 大毒使い → Master of Poison
DeepSeekTranslationService: Name mapping - 黒い血 → Black Blood
DeepSeekTranslationService: Name mapping - 毒沼操作 → Poison Swamp Control
DeepSeekTranslationService: Name mapping - 毒草プランター → Poison Herb Planter
DeepSeekTranslationService: Name mapping - 毒手切り替え → Poison Hand Switch
DeepSeekTranslationService: Name mapping - 毒蛇会話 → Speak with Poison Snakes
DeepSeekTranslationService: Name mapping - 中和毒 → Neutralizing Poison
DeepSeekTranslationService: Name mapping - 気化毒 → Vaporized Poison
DeepSeekTranslationService: Name mapping - 薄まった毒 → Diluted Poison
DeepSeekTranslationService: Name mapping - 毒の武器 → Poison Weapon
DeepSeekTranslationService: Name mapping - 毒分身 → Poison Clone
DeepSeekTranslationService: Name mapping - 回復薬生成Ⅰ → Recovery Potion Synthesis I
DeepSeekTranslationService: Name mapping - 外敵の知らせⅠ → Enemy Alert I
DeepSeekTranslationService: Name mapping - 毒手スキルツリー《達人級》 → Poison Hand Skill Tree [Master Level]
DeepSeekTranslationService: Name mapping - 竜 → Dragon
DeepSeekTranslationService: Name mapping - ムカデ → Giant Centipede
DeepSeekTranslationService: Name mapping - 毒牙のガルム → Garum of the Poison Fang
DeepSeekTranslationService: Name mapping - 髑髏騎士 → Skull Knight
DeepSeekTranslationService: Name mapping - 衝撃波 → Shockwave
DeepSeekTranslationService: Name mapping - ウッドエルフ → Wood Elf
DeepSeekTranslationService: Name mapping - チュートリア → Tutorial
DeepSeekTranslationService: Name mapping - プレイヤー → player
DeepSeekTranslationService: Name mapping - スキル → Skill
DeepSeekTranslationService: Name mapping - ページ → page
DeepSeekTranslationService: Name mapping - Sukiru Tsurī → Skill Tree
DeepSeekTranslationService: Name mapping - スキル経験値 → Skill Experience Points
DeepSeekTranslationService: Name mapping - スキルツリー → Skill Tree
DeepSeekTranslationService: Name mapping - 無痛症 → Congenital Analgesia
DeepSeekTranslationService: Name mapping - 不感症 → Sensory Deficiency
DeepSeekTranslationService: Name mapping - 魔力点穴強制開門 → Forced Mana Point Opening
DeepSeekTranslationService: Name mapping - スキルホルダー → Skill Holder
DeepSeekTranslationService: Name mapping - 味覚薬 → Taste Medicine
DeepSeekTranslationService: Name mapping - 昼だまり草 → Sunlit Herb
DeepSeekTranslationService: Name mapping - 無限のビン → Bottomless Bottle
DeepSeekTranslationService: Name mapping - 無痛薬 → Painkiller (Legendary)
DeepSeekTranslationService: Name mapping - 無限の空き瓶 → Infinite Empty Bottle
DeepSeekTranslationService: Name mapping - 追放者のフード → Exile’s Hood
DeepSeekTranslationService: Name mapping - 薬師のローブ → Apothecary’s Robe
DeepSeekTranslationService: Name mapping - 革の旅ブーツ → Leather Travel Boots
DeepSeekTranslationService: Name mapping - 回復薬 → Recovery Potion
DeepSeekTranslationService: Name mapping - 味覚薬レシピ → Taste Potion Recipe
DeepSeekTranslationService: Name mapping - 薬師 → Apothecary
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Episode 17: Day 10 - Wyvern Crash
DeepSeekTranslationService: Cleaned title result: Episode 17: Day 10 - Wyvern Crash
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation, Significant punctuation loss detected
TranslationService: Original title response: Episode 17: Day 10 - Wyvern Crash
TranslationService: Cleaned title result: Episode 17: Day 10 - Wyvern Crash
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 78 entries
DeepSeekTranslationService: Name mapping - エルマ → Elma
DeepSeekTranslationService: Name mapping - タボ → Tabo
DeepSeekTranslationService: Name mapping - パーラハーラ3世 → Pārahāra Sansei
DeepSeekTranslationService: Name mapping - ステータス → Status
DeepSeekTranslationService: Name mapping - カバン → kaban
DeepSeekTranslationService: Name mapping - パラちゃん → Parachan
DeepSeekTranslationService: Name mapping - パパ → Papa
DeepSeekTranslationService: Name mapping - レイ → Rei
DeepSeekTranslationService: Name mapping - 世界樹林・死王の蟲野営地 → World Tree Forest・Insect King's Campsite
DeepSeekTranslationService: Name mapping - トアイラ世界樹林 → World Tree Forest
DeepSeekTranslationService: Name mapping - 死王の蟲野営地 → Dead King&#039;s Insect Encampment
DeepSeekTranslationService: Name mapping - 植物園 → Botanical Garden
DeepSeekTranslationService: Name mapping - 異世界 → Isekai
DeepSeekTranslationService: Name mapping - 冒険者ギルド → Adventurers Guild
DeepSeekTranslationService: Name mapping - エルダーフロンティア → Elder Frontiers
DeepSeekTranslationService: Name mapping - ギルド → guild
DeepSeekTranslationService: Name mapping - 一二神教 → Twelve Deities Church
DeepSeekTranslationService: Name mapping - 教会騎士団 → Church Knights
DeepSeekTranslationService: Name mapping - 毒スキル → Poison Skill
DeepSeekTranslationService: Name mapping - 薬師スキル → Pharmacist Skill
DeepSeekTranslationService: Name mapping - 毒手スキル → Poison Hand Skill
DeepSeekTranslationService: Name mapping - 外敵の察知スキル → Enemy Detection Skill
DeepSeekTranslationService: Name mapping - 毒手 → Poison Hand
DeepSeekTranslationService: Name mapping - 毒喰らい → Poison Eater
DeepSeekTranslationService: Name mapping - 毒術 → Poison Arts
DeepSeekTranslationService: Name mapping - 装 → Armament
DeepSeekTranslationService: Name mapping - 集 → Concentration
DeepSeekTranslationService: Name mapping - 変 → Transformation
DeepSeekTranslationService: Name mapping - 素材感知Ⅰ → Material Detection I
DeepSeekTranslationService: Name mapping - 外敵の察知 → Foe Detection
DeepSeekTranslationService: Name mapping - 友村友人 → Tomohito Tomomura
DeepSeekTranslationService: Name mapping - 毒喰らいⅠ → Poison Eater I
DeepSeekTranslationService: Name mapping - 毒調合Ⅰ → Poison Mixing I
DeepSeekTranslationService: Name mapping - 毒解析Ⅰ → Poison Analysis I
DeepSeekTranslationService: Name mapping - 麻痺毒Ⅰ → Paralysis Poison I
DeepSeekTranslationService: Name mapping - 毒の高揚 → Poison Excitement
DeepSeekTranslationService: Name mapping - 大毒使い → Master of Poison
DeepSeekTranslationService: Name mapping - 黒い血 → Black Blood
DeepSeekTranslationService: Name mapping - 毒沼操作 → Poison Swamp Control
DeepSeekTranslationService: Name mapping - 毒草プランター → Poison Herb Planter
DeepSeekTranslationService: Name mapping - 毒手切り替え → Poison Hand Switch
DeepSeekTranslationService: Name mapping - 毒蛇会話 → Speak with Poison Snakes
DeepSeekTranslationService: Name mapping - 中和毒 → Neutralizing Poison
DeepSeekTranslationService: Name mapping - 気化毒 → Vaporized Poison
DeepSeekTranslationService: Name mapping - 薄まった毒 → Diluted Poison
DeepSeekTranslationService: Name mapping - 毒の武器 → Poison Weapon
DeepSeekTranslationService: Name mapping - 毒分身 → Poison Clone
DeepSeekTranslationService: Name mapping - 回復薬生成Ⅰ → Recovery Potion Synthesis I
DeepSeekTranslationService: Name mapping - 外敵の知らせⅠ → Enemy Alert I
DeepSeekTranslationService: Name mapping - 毒手スキルツリー《達人級》 → Poison Hand Skill Tree [Master Level]
DeepSeekTranslationService: Name mapping - 竜 → Dragon
DeepSeekTranslationService: Name mapping - ムカデ → Giant Centipede
DeepSeekTranslationService: Name mapping - 毒牙のガルム → Garum of the Poison Fang
DeepSeekTranslationService: Name mapping - 髑髏騎士 → Skull Knight
DeepSeekTranslationService: Name mapping - 衝撃波 → Shockwave
DeepSeekTranslationService: Name mapping - ウッドエルフ → Wood Elf
DeepSeekTranslationService: Name mapping - チュートリア → Tutorial
DeepSeekTranslationService: Name mapping - プレイヤー → player
DeepSeekTranslationService: Name mapping - スキル → Skill
DeepSeekTranslationService: Name mapping - ページ → page
DeepSeekTranslationService: Name mapping - Sukiru Tsurī → Skill Tree
DeepSeekTranslationService: Name mapping - スキル経験値 → Skill Experience Points
DeepSeekTranslationService: Name mapping - スキルツリー → Skill Tree
DeepSeekTranslationService: Name mapping - 無痛症 → Congenital Analgesia
DeepSeekTranslationService: Name mapping - 不感症 → Sensory Deficiency
DeepSeekTranslationService: Name mapping - 魔力点穴強制開門 → Forced Mana Point Opening
DeepSeekTranslationService: Name mapping - スキルホルダー → Skill Holder
DeepSeekTranslationService: Name mapping - 味覚薬 → Taste Medicine
DeepSeekTranslationService: Name mapping - 昼だまり草 → Sunlit Herb
DeepSeekTranslationService: Name mapping - 無限のビン → Bottomless Bottle
DeepSeekTranslationService: Name mapping - 無痛薬 → Painkiller (Legendary)
DeepSeekTranslationService: Name mapping - 無限の空き瓶 → Infinite Empty Bottle
DeepSeekTranslationService: Name mapping - 追放者のフード → Exile’s Hood
DeepSeekTranslationService: Name mapping - 薬師のローブ → Apothecary’s Robe
DeepSeekTranslationService: Name mapping - 革の旅ブーツ → Leather Travel Boots
DeepSeekTranslationService: Name mapping - 回復薬 → Recovery Potion
DeepSeekTranslationService: Name mapping - 味覚薬レシピ → Taste Potion Recipe
DeepSeekTranslationService: Name mapping - 薬師 → Apothecary
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 「 at position 552
DeepSeekTranslationService: Restored " → 」 at position 566
DeepSeekTranslationService: Restored " → 「 at position 694
DeepSeekTranslationService: Restored " → 」 at position 723
DeepSeekTranslationService: Restored " → 「 at position 904
DeepSeekTranslationService: Restored " → 」 at position 968
DeepSeekTranslationService: Restored ~ → ～ at position 1249
DeepSeekTranslationService: Restored ~ → ～ at position 1250
DeepSeekTranslationService: Restored ~ → ～ at position 1251
DeepSeekTranslationService: Restored ~ → ～ at position 1425
DeepSeekTranslationService: Restored ~ → ～ at position 1426
DeepSeekTranslationService: Restored ~ → ～ at position 1427
DeepSeekTranslationService: Restored " → 「 at position 1367
DeepSeekTranslationService: Restored " → 」 at position 1419
DeepSeekTranslationService: Restored " → 「 at position 1519
DeepSeekTranslationService: Restored " → 」 at position 1561
DeepSeekTranslationService: Restored " → 「 at position 1588
DeepSeekTranslationService: Restored " → 」 at position 1612
DeepSeekTranslationService: Restored " → 「 at position 2390
DeepSeekTranslationService: Restored " → 」 at position 2406
DeepSeekTranslationService: Restored " → 「 at position 2413
DeepSeekTranslationService: Restored " → 」 at position 2434
DeepSeekTranslationService: Restored " → 「 at position 2601
DeepSeekTranslationService: Restored " → 」 at position 2635
DeepSeekTranslationService: Restored " → 「 at position 2642
DeepSeekTranslationService: Restored " → 」 at position 2648
DeepSeekTranslationService: Restored " → 「 at position 2850
DeepSeekTranslationService: Restored " → 」 at position 2856
DeepSeekTranslationService: Restored " → 「 at position 3091
DeepSeekTranslationService: Restored " → 」 at position 3155
DeepSeekTranslationService: Restored " → 「 at position 3162
DeepSeekTranslationService: Restored " → 」 at position 3180
DeepSeekTranslationService: Restored " → 「 at position 3478
DeepSeekTranslationService: Restored " → 」 at position 3501
DeepSeekTranslationService: Restored " → 「 at position 3508
DeepSeekTranslationService: Restored " → 」 at position 3557
DeepSeekTranslationService: Restored " → 「 at position 3673
DeepSeekTranslationService: Restored " → 」 at position 3678
DeepSeekTranslationService: Restored " → 「 at position 3872
DeepSeekTranslationService: Restored " → 」 at position 3893
DeepSeekTranslationService: Restored " → 「 at position 4057
DeepSeekTranslationService: Restored " → 」 at position 4067
DeepSeekTranslationService: Restored " → 「 at position 4158
DeepSeekTranslationService: Restored " → 」 at position 4164
DeepSeekTranslationService: Restored " → 「 at position 4171
DeepSeekTranslationService: Restored " → 」 at position 4176
DeepSeekTranslationService: Restored " → 「 at position 4391
DeepSeekTranslationService: Restored " → 」 at position 4406
DeepSeekTranslationService: Restored " → 「 at position 4474
DeepSeekTranslationService: Restored " → 」 at position 4481
DeepSeekTranslationService: Restored " → 「 at position 4488
DeepSeekTranslationService: Restored " → 」 at position 4492
DeepSeekTranslationService: Restored " → 「 at position 4740
DeepSeekTranslationService: Restored " → 」 at position 4749
DeepSeekTranslationService: Restored " → 「 at position 5488
DeepSeekTranslationService: Restored " → 」 at position 5512
DeepSeekTranslationService: Restored " → 「 at position 5519
DeepSeekTranslationService: Restored " → 」 at position 5524
DeepSeekTranslationService: Restored " → 「 at position 5627
DeepSeekTranslationService: Restored " → 」 at position 5653
DeepSeekTranslationService: Restored " → 「 at position 5690
DeepSeekTranslationService: Restored " → 」 at position 5716
DeepSeekTranslationService: Restored " → 「 at position 5823
DeepSeekTranslationService: Restored " → 」 at position 5829
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 7605
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: 'たぼ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'エルマ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ウッドエルフ' (matched pattern: /^[。！？、，]/)
TranslationService: AI parsed 2 high-quality names after filtering
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: AI detected 2 names
TranslationService: Using AI-detected names only: 2 names found
TranslationService: Enhancing 2 names with translations
TranslationService: Using romanization as translation for '世界樹林': 'Shìjiè shùlín'
TranslationService: Using romanization as translation for '死王の蟲': 'Shiō no mushi'
TranslationService: Successfully extracted 2 names with translations
TranslationService: Found 2 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":17,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":3,"chapter_number":18,"target_language":"en"}
Starting translate - Novel ID: 3, Chapters: 18, Language: en
TranslationService: Starting chapter translation - Novel ID: 3, Chapter ID: 385
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第18話　10日目:セーブザワイバーン
TranslationService: Chapter content length: 5674
TranslationService: getNameDictionary query returned 85 entries for novel 3
TranslationService: Name dictionary entries: 85
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 85 entries
DeepSeekTranslationService: Name mapping - 死王の蟲 → Shiō no mushi
DeepSeekTranslationService: Name mapping - エルマ → Elma
DeepSeekTranslationService: Name mapping - タボ → Tabo
DeepSeekTranslationService: Name mapping - パーラハーラ3世 → Pārahāra Sansei
DeepSeekTranslationService: Name mapping - ステータス → Status
DeepSeekTranslationService: Name mapping - カバン → kaban
DeepSeekTranslationService: Name mapping - パラちゃん → Parachan
DeepSeekTranslationService: Name mapping - パパ → Papa
DeepSeekTranslationService: Name mapping - レイ → Rei
DeepSeekTranslationService: Name mapping - 世界樹林・死王の蟲野営地 → World Tree Forest・Insect King's Campsite
DeepSeekTranslationService: Name mapping - 世界樹林 → Shìjiè shùlín
DeepSeekTranslationService: Name mapping - トアイラ世界樹林 → World Tree Forest
DeepSeekTranslationService: Name mapping - 死王の蟲野営地 → Dead King&#039;s Insect Encampment
DeepSeekTranslationService: Name mapping - 植物園 → Botanical Garden
DeepSeekTranslationService: Name mapping - 異世界 → Isekai
DeepSeekTranslationService: Name mapping - 冒険者ギルド → Adventurers Guild
DeepSeekTranslationService: Name mapping - エルダーフロンティア → Elder Frontiers
DeepSeekTranslationService: Name mapping - ギルド → guild
DeepSeekTranslationService: Name mapping - 一二神教 → Twelve Deities Church
DeepSeekTranslationService: Name mapping - 教会騎士団 → Church Knights
DeepSeekTranslationService: Name mapping - 毒スキル → Poison Skill
DeepSeekTranslationService: Name mapping - 薬師スキル → Pharmacist Skill
DeepSeekTranslationService: Name mapping - 毒手スキル → Poison Hand Skill
DeepSeekTranslationService: Name mapping - 外敵の察知スキル → Enemy Detection Skill
DeepSeekTranslationService: Name mapping - 毒煙の術 → Poison Smoke Art
DeepSeekTranslationService: Name mapping - 大毒使いスキル → Grand Poison User Skill
DeepSeekTranslationService: Name mapping - 毒分身の術 → Poison Clone Art
DeepSeekTranslationService: Name mapping - 禁術 → Forbidden Spell
DeepSeekTranslationService: Name mapping - 毒手 → Poison Hand
DeepSeekTranslationService: Name mapping - 毒喰らい → Poison Eater
DeepSeekTranslationService: Name mapping - 毒術 → Poison Arts
DeepSeekTranslationService: Name mapping - 装 → Armament
DeepSeekTranslationService: Name mapping - 集 → Concentration
DeepSeekTranslationService: Name mapping - 変 → Transformation
DeepSeekTranslationService: Name mapping - 素材感知Ⅰ → Material Detection I
DeepSeekTranslationService: Name mapping - 外敵の察知 → Foe Detection
DeepSeekTranslationService: Name mapping - 友村友人 → Tomohito Tomomura
DeepSeekTranslationService: Name mapping - 毒喰らいⅠ → Poison Eater I
DeepSeekTranslationService: Name mapping - 毒調合Ⅰ → Poison Mixing I
DeepSeekTranslationService: Name mapping - 毒解析Ⅰ → Poison Analysis I
DeepSeekTranslationService: Name mapping - 麻痺毒Ⅰ → Paralysis Poison I
DeepSeekTranslationService: Name mapping - 毒の高揚 → Poison Excitement
DeepSeekTranslationService: Name mapping - 大毒使い → Master of Poison
DeepSeekTranslationService: Name mapping - 黒い血 → Black Blood
DeepSeekTranslationService: Name mapping - 毒沼操作 → Poison Swamp Control
DeepSeekTranslationService: Name mapping - 毒草プランター → Poison Herb Planter
DeepSeekTranslationService: Name mapping - 毒手切り替え → Poison Hand Switch
DeepSeekTranslationService: Name mapping - 毒蛇会話 → Speak with Poison Snakes
DeepSeekTranslationService: Name mapping - 中和毒 → Neutralizing Poison
DeepSeekTranslationService: Name mapping - 気化毒 → Vaporized Poison
DeepSeekTranslationService: Name mapping - 薄まった毒 → Diluted Poison
DeepSeekTranslationService: Name mapping - 毒の武器 → Poison Weapon
DeepSeekTranslationService: Name mapping - 毒分身 → Poison Clone
DeepSeekTranslationService: Name mapping - 回復薬生成Ⅰ → Recovery Potion Synthesis I
DeepSeekTranslationService: Name mapping - 外敵の知らせⅠ → Enemy Alert I
DeepSeekTranslationService: Name mapping - 毒手スキルツリー《達人級》 → Poison Hand Skill Tree [Master Level]
DeepSeekTranslationService: Name mapping - 竜 → Dragon
DeepSeekTranslationService: Name mapping - ムカデ → Giant Centipede
DeepSeekTranslationService: Name mapping - 毒牙のガルム → Garum of the Poison Fang
DeepSeekTranslationService: Name mapping - 髑髏騎士 → Skull Knight
DeepSeekTranslationService: Name mapping - 衝撃波 → Shockwave
DeepSeekTranslationService: Name mapping - ウッドエルフ → Wood Elf
DeepSeekTranslationService: Name mapping - チュートリア → Tutorial
DeepSeekTranslationService: Name mapping - プレイヤー → player
DeepSeekTranslationService: Name mapping - スキル → Skill
DeepSeekTranslationService: Name mapping - ページ → page
DeepSeekTranslationService: Name mapping - Sukiru Tsurī → Skill Tree
DeepSeekTranslationService: Name mapping - スキル経験値 → Skill Experience Points
DeepSeekTranslationService: Name mapping - スキルツリー → Skill Tree
DeepSeekTranslationService: Name mapping - 無痛症 → Congenital Analgesia
DeepSeekTranslationService: Name mapping - 不感症 → Sensory Deficiency
DeepSeekTranslationService: Name mapping - 魔力点穴強制開門 → Forced Mana Point Opening
DeepSeekTranslationService: Name mapping - スキルホルダー → Skill Holder
DeepSeekTranslationService: Name mapping - ボウガン → Crossbow
DeepSeekTranslationService: Name mapping - 味覚薬 → Taste Medicine
DeepSeekTranslationService: Name mapping - 昼だまり草 → Sunlit Herb
DeepSeekTranslationService: Name mapping - 無限のビン → Bottomless Bottle
DeepSeekTranslationService: Name mapping - 無痛薬 → Painkiller (Legendary)
DeepSeekTranslationService: Name mapping - 無限の空き瓶 → Infinite Empty Bottle
DeepSeekTranslationService: Name mapping - 追放者のフード → Exile’s Hood
DeepSeekTranslationService: Name mapping - 薬師のローブ → Apothecary’s Robe
DeepSeekTranslationService: Name mapping - 革の旅ブーツ → Leather Travel Boots
DeepSeekTranslationService: Name mapping - 回復薬 → Recovery Potion
DeepSeekTranslationService: Name mapping - 味覚薬レシピ → Taste Potion Recipe
DeepSeekTranslationService: Name mapping - 薬師 → Apothecary
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Episode 18: Day 10 - Save the Wyvern
DeepSeekTranslationService: Cleaned title result: Episode 18: Day 10 - Save the Wyvern
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation, Significant punctuation loss detected
TranslationService: Original title response: Episode 18: Day 10 - Save the Wyvern
TranslationService: Cleaned title result: Episode 18: Day 10 - Save the Wyvern
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 85 entries
DeepSeekTranslationService: Name mapping - 死王の蟲 → Shiō no mushi
DeepSeekTranslationService: Name mapping - エルマ → Elma
DeepSeekTranslationService: Name mapping - タボ → Tabo
DeepSeekTranslationService: Name mapping - パーラハーラ3世 → Pārahāra Sansei
DeepSeekTranslationService: Name mapping - ステータス → Status
DeepSeekTranslationService: Name mapping - カバン → kaban
DeepSeekTranslationService: Name mapping - パラちゃん → Parachan
DeepSeekTranslationService: Name mapping - パパ → Papa
DeepSeekTranslationService: Name mapping - レイ → Rei
DeepSeekTranslationService: Name mapping - 世界樹林・死王の蟲野営地 → World Tree Forest・Insect King's Campsite
DeepSeekTranslationService: Name mapping - 世界樹林 → Shìjiè shùlín
DeepSeekTranslationService: Name mapping - トアイラ世界樹林 → World Tree Forest
DeepSeekTranslationService: Name mapping - 死王の蟲野営地 → Dead King&#039;s Insect Encampment
DeepSeekTranslationService: Name mapping - 植物園 → Botanical Garden
DeepSeekTranslationService: Name mapping - 異世界 → Isekai
DeepSeekTranslationService: Name mapping - 冒険者ギルド → Adventurers Guild
DeepSeekTranslationService: Name mapping - エルダーフロンティア → Elder Frontiers
DeepSeekTranslationService: Name mapping - ギルド → guild
DeepSeekTranslationService: Name mapping - 一二神教 → Twelve Deities Church
DeepSeekTranslationService: Name mapping - 教会騎士団 → Church Knights
DeepSeekTranslationService: Name mapping - 毒スキル → Poison Skill
DeepSeekTranslationService: Name mapping - 薬師スキル → Pharmacist Skill
DeepSeekTranslationService: Name mapping - 毒手スキル → Poison Hand Skill
DeepSeekTranslationService: Name mapping - 外敵の察知スキル → Enemy Detection Skill
DeepSeekTranslationService: Name mapping - 毒煙の術 → Poison Smoke Art
DeepSeekTranslationService: Name mapping - 大毒使いスキル → Grand Poison User Skill
DeepSeekTranslationService: Name mapping - 毒分身の術 → Poison Clone Art
DeepSeekTranslationService: Name mapping - 禁術 → Forbidden Spell
DeepSeekTranslationService: Name mapping - 毒手 → Poison Hand
DeepSeekTranslationService: Name mapping - 毒喰らい → Poison Eater
DeepSeekTranslationService: Name mapping - 毒術 → Poison Arts
DeepSeekTranslationService: Name mapping - 装 → Armament
DeepSeekTranslationService: Name mapping - 集 → Concentration
DeepSeekTranslationService: Name mapping - 変 → Transformation
DeepSeekTranslationService: Name mapping - 素材感知Ⅰ → Material Detection I
DeepSeekTranslationService: Name mapping - 外敵の察知 → Foe Detection
DeepSeekTranslationService: Name mapping - 友村友人 → Tomohito Tomomura
DeepSeekTranslationService: Name mapping - 毒喰らいⅠ → Poison Eater I
DeepSeekTranslationService: Name mapping - 毒調合Ⅰ → Poison Mixing I
DeepSeekTranslationService: Name mapping - 毒解析Ⅰ → Poison Analysis I
DeepSeekTranslationService: Name mapping - 麻痺毒Ⅰ → Paralysis Poison I
DeepSeekTranslationService: Name mapping - 毒の高揚 → Poison Excitement
DeepSeekTranslationService: Name mapping - 大毒使い → Master of Poison
DeepSeekTranslationService: Name mapping - 黒い血 → Black Blood
DeepSeekTranslationService: Name mapping - 毒沼操作 → Poison Swamp Control
DeepSeekTranslationService: Name mapping - 毒草プランター → Poison Herb Planter
DeepSeekTranslationService: Name mapping - 毒手切り替え → Poison Hand Switch
DeepSeekTranslationService: Name mapping - 毒蛇会話 → Speak with Poison Snakes
DeepSeekTranslationService: Name mapping - 中和毒 → Neutralizing Poison
DeepSeekTranslationService: Name mapping - 気化毒 → Vaporized Poison
DeepSeekTranslationService: Name mapping - 薄まった毒 → Diluted Poison
DeepSeekTranslationService: Name mapping - 毒の武器 → Poison Weapon
DeepSeekTranslationService: Name mapping - 毒分身 → Poison Clone
DeepSeekTranslationService: Name mapping - 回復薬生成Ⅰ → Recovery Potion Synthesis I
DeepSeekTranslationService: Name mapping - 外敵の知らせⅠ → Enemy Alert I
DeepSeekTranslationService: Name mapping - 毒手スキルツリー《達人級》 → Poison Hand Skill Tree [Master Level]
DeepSeekTranslationService: Name mapping - 竜 → Dragon
DeepSeekTranslationService: Name mapping - ムカデ → Giant Centipede
DeepSeekTranslationService: Name mapping - 毒牙のガルム → Garum of the Poison Fang
DeepSeekTranslationService: Name mapping - 髑髏騎士 → Skull Knight
DeepSeekTranslationService: Name mapping - 衝撃波 → Shockwave
DeepSeekTranslationService: Name mapping - ウッドエルフ → Wood Elf
DeepSeekTranslationService: Name mapping - チュートリア → Tutorial
DeepSeekTranslationService: Name mapping - プレイヤー → player
DeepSeekTranslationService: Name mapping - スキル → Skill
DeepSeekTranslationService: Name mapping - ページ → page
DeepSeekTranslationService: Name mapping - Sukiru Tsurī → Skill Tree
DeepSeekTranslationService: Name mapping - スキル経験値 → Skill Experience Points
DeepSeekTranslationService: Name mapping - スキルツリー → Skill Tree
DeepSeekTranslationService: Name mapping - 無痛症 → Congenital Analgesia
DeepSeekTranslationService: Name mapping - 不感症 → Sensory Deficiency
DeepSeekTranslationService: Name mapping - 魔力点穴強制開門 → Forced Mana Point Opening
DeepSeekTranslationService: Name mapping - スキルホルダー → Skill Holder
DeepSeekTranslationService: Name mapping - ボウガン → Crossbow
DeepSeekTranslationService: Name mapping - 味覚薬 → Taste Medicine
DeepSeekTranslationService: Name mapping - 昼だまり草 → Sunlit Herb
DeepSeekTranslationService: Name mapping - 無限のビン → Bottomless Bottle
DeepSeekTranslationService: Name mapping - 無痛薬 → Painkiller (Legendary)
DeepSeekTranslationService: Name mapping - 無限の空き瓶 → Infinite Empty Bottle
DeepSeekTranslationService: Name mapping - 追放者のフード → Exile’s Hood
DeepSeekTranslationService: Name mapping - 薬師のローブ → Apothecary’s Robe
DeepSeekTranslationService: Name mapping - 革の旅ブーツ → Leather Travel Boots
DeepSeekTranslationService: Name mapping - 回復薬 → Recovery Potion
DeepSeekTranslationService: Name mapping - 味覚薬レシピ → Taste Potion Recipe
DeepSeekTranslationService: Name mapping - 薬師 → Apothecary
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 「 at position 0
DeepSeekTranslationService: Restored " → 」 at position 22
DeepSeekTranslationService: Restored " → 「 at position 25
DeepSeekTranslationService: Restored " → 」 at position 54
DeepSeekTranslationService: Restored " → 「 at position 388
DeepSeekTranslationService: Restored " → 」 at position 417
DeepSeekTranslationService: Restored " → 「 at position 420
DeepSeekTranslationService: Restored " → 」 at position 426
DeepSeekTranslationService: Restored " → 「 at position 429
DeepSeekTranslationService: Restored " → 」 at position 444
DeepSeekTranslationService: Restored " → 「 at position 717
DeepSeekTranslationService: Restored " → 」 at position 729
DeepSeekTranslationService: Restored " → 「 at position 1064
DeepSeekTranslationService: Restored " → 」 at position 1121
DeepSeekTranslationService: Restored " → 「 at position 1124
DeepSeekTranslationService: Restored " → 」 at position 1139
DeepSeekTranslationService: Restored " → 「 at position 1217
DeepSeekTranslationService: Restored " → 」 at position 1241
DeepSeekTranslationService: Restored " → 「 at position 1244
DeepSeekTranslationService: Restored " → 」 at position 1250
DeepSeekTranslationService: Restored " → 「 at position 1506
DeepSeekTranslationService: Restored " → 」 at position 1550
DeepSeekTranslationService: Restored " → 「 at position 1626
DeepSeekTranslationService: Restored " → 」 at position 1632
DeepSeekTranslationService: Restored " → 「 at position 1634
DeepSeekTranslationService: Restored " → 」 at position 1642
DeepSeekTranslationService: Restored " → 「 at position 2044
DeepSeekTranslationService: Restored " → 」 at position 2101
DeepSeekTranslationService: Restored " → 「 at position 2104
DeepSeekTranslationService: Restored " → 」 at position 2110
DeepSeekTranslationService: Restored " → 「 at position 2349
DeepSeekTranslationService: Restored " → 」 at position 2367
DeepSeekTranslationService: Restored " → 「 at position 2564
DeepSeekTranslationService: Restored " → 」 at position 2570
DeepSeekTranslationService: Restored " → 「 at position 2573
DeepSeekTranslationService: Restored " → 」 at position 2583
DeepSeekTranslationService: Restored " → 「 at position 2954
DeepSeekTranslationService: Restored " → 」 at position 2959
DeepSeekTranslationService: Restored " → 「 at position 3077
DeepSeekTranslationService: Restored " → 」 at position 3082
DeepSeekTranslationService: Restored " → 「 at position 3173
DeepSeekTranslationService: Restored " → 」 at position 3186
DeepSeekTranslationService: Restored " → 「 at position 3207
DeepSeekTranslationService: Restored " → 「 at position 3261
DeepSeekTranslationService: Restored " → 「 at position 3264
DeepSeekTranslationService: Restored " → 「 at position 3265
DeepSeekTranslationService: Restored " → 」 at position 3266
DeepSeekTranslationService: Restored " → 」 at position 3267
DeepSeekTranslationService: Restored " → 」 at position 3272
DeepSeekTranslationService: Restored " → 「 at position 3273
DeepSeekTranslationService: Restored " → 」 at position 3274
DeepSeekTranslationService: Restored " → 「 at position 3275
DeepSeekTranslationService: Restored " → 」 at position 3881
DeepSeekTranslationService: Restored " → 「 at position 3889
DeepSeekTranslationService: Restored " → 」 at position 3954
DeepSeekTranslationService: Restored " → 「 at position 3970
DeepSeekTranslationService: Restored " → 」 at position 4096
DeepSeekTranslationService: Restored " → 「 at position 4207
DeepSeekTranslationService: Restored " → 」 at position 4210
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 5674
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: 'タボ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ワイバーン' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ゴブリン' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'エルマ' (matched pattern: /^[。！？、，]/)
TranslationService: AI parsed 3 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Selective filtering reduced pattern names from 10 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":18,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":6,"chapter_number":3,"target_language":"en"}
Starting translate - Novel ID: 6, Chapters: 3, Language: en
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 480
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第3章 公主
TranslationService: Chapter content length: 6664
TranslationService: getNameDictionary query returned 27 entries for novel 6
TranslationService: Name dictionary entries: 27
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 27 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhào Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhū cí xià
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 东华大学 → Dōnghuá Dàxué
DeepSeekTranslationService: Name mapping - 班级群 → bānjí qún
DeepSeekTranslationService: Name mapping - 网龙 → Wǎnglóng
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 虚拟世界 → Xūnǐ shìjiè
DeepSeekTranslationService: Name mapping - 图灵奖 → Túlíng jiǎng
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → nǎobō shénjīng jiāohù shèbèi
DeepSeekTranslationService: Name mapping - 可控核聚变 → kěkòng héjùbiàn
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Chapter 3: The Princess
DeepSeekTranslationService: Cleaned title result: Chapter 3: The Princess
TranslationService: DeepSeek translation successful
TranslationService: Original title response: Chapter 3: The Princess
TranslationService: Cleaned title result: Chapter 3: The Princess
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 27 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhào Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhū cí xià
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 东华大学 → Dōnghuá Dàxué
DeepSeekTranslationService: Name mapping - 班级群 → bānjí qún
DeepSeekTranslationService: Name mapping - 网龙 → Wǎnglóng
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 虚拟世界 → Xūnǐ shìjiè
DeepSeekTranslationService: Name mapping - 图灵奖 → Túlíng jiǎng
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → nǎobō shénjīng jiāohù shèbèi
DeepSeekTranslationService: Name mapping - 可控核聚变 → kěkòng héjùbiàn
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Paragraph structure significantly changed: 54 → 51, Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 6664
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: AI parsed 3 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Selective filtering reduced pattern names from 0 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":3,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":6,"chapter_number":3,"target_language":"en"}
Starting translate - Novel ID: 6, Chapters: 3, Language: en
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 480
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第3章 公主
TranslationService: Chapter content length: 6664
TranslationService: getNameDictionary query returned 38 entries for novel 6
TranslationService: Name dictionary entries: 38
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 38 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Chapter 3: The Princess
DeepSeekTranslationService: Cleaned title result: Chapter 3: The Princess
TranslationService: DeepSeek translation successful
TranslationService: Original title response: Chapter 3: The Princess
TranslationService: Cleaned title result: Chapter 3: The Princess
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 38 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 6664
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: AI parsed 3 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Selective filtering reduced pattern names from 0 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":3,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":6,"chapter_number":4,"target_language":"en"}
Starting translate - Novel ID: 6, Chapters: 4, Language: en
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 481
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第4章 网龙的反击
TranslationService: Chapter content length: 6551
TranslationService: getNameDictionary query returned 42 entries for novel 6
TranslationService: Name dictionary entries: 42
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 42 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Chapter 4: NetDragon's Counterattack
DeepSeekTranslationService: Cleaned title result: Chapter 4: NetDragon's Counterattack
TranslationService: DeepSeek translation successful
TranslationService: Original title response: Chapter 4: NetDragon's Counterattack
TranslationService: Cleaned title result: Chapter 4: NetDragon's Counterattack
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 42 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 《 at position 732
DeepSeekTranslationService: Restored " → 》 at position 947
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 6551
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: AI parsed 5 high-quality names after filtering
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: AI detected 1 names
TranslationService: Using AI-detected names only: 1 names found
TranslationService: Enhancing 1 names with translations
TranslationService: Using romanization as translation for '图-160': 'Tu-160'
TranslationService: Successfully extracted 1 names with translations
TranslationService: Found 1 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":4,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":6,"chapter_number":5,"target_language":"en"}
Starting translate - Novel ID: 6, Chapters: 5, Language: en
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 482
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第5章 小梦
TranslationService: Chapter content length: 6583
TranslationService: getNameDictionary query returned 48 entries for novel 6
TranslationService: Name dictionary entries: 48
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 48 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Chapter 5: Xiaomeng
DeepSeekTranslationService: Cleaned title result: Chapter 5: Xiaomeng
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation
TranslationService: Original title response: Chapter 5: Xiaomeng
TranslationService: Cleaned title result: Chapter 5: Xiaomeng
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 48 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 6583
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: AI parsed 3 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Selective filtering reduced pattern names from 0 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":5,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":6,"chapter_number":6,"target_language":"en"}
Starting translate - Novel ID: 6, Chapters: 6, Language: en
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 483
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第6章 开始做游戏
TranslationService: Chapter content length: 6701
TranslationService: getNameDictionary query returned 58 entries for novel 6
TranslationService: Name dictionary entries: 58
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 58 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Chapter 6: Starting Game Development
DeepSeekTranslationService: Cleaned title result: Chapter 6: Starting Game Development
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation, Significant punctuation loss detected
TranslationService: Original title response: Chapter 6: Starting Game Development
TranslationService: Cleaned title result: Chapter 6: Starting Game Development
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 58 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 《 at position 0
DeepSeekTranslationService: Restored " → 》 at position 12
DeepSeekTranslationService: Restored " → 《 at position 73
DeepSeekTranslationService: Restored " → 》 at position 183
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 6701
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: '《刺客联盟》' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '《通缉令》' (matched pattern: /^[。！？、，]/)
TranslationService: AI parsed 5 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Selective filtering reduced pattern names from 0 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":6,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":6,"chapter_number":7,"target_language":"en"}
Starting translate - Novel ID: 6, Chapters: 7, Language: en
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 484
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第7章 要不还是先算了
TranslationService: Chapter content length: 7581
TranslationService: getNameDictionary query returned 59 entries for novel 6
TranslationService: Name dictionary entries: 59
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 59 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess (referring to Zhu Cixia)
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Chapter 7 Maybe We Should Just Drop It for Now
DeepSeekTranslationService: Cleaned title result: Chapter 7 Maybe We Should Just Drop It for Now
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation, Significant punctuation loss detected
TranslationService: Original title response: Chapter 7 Maybe We Should Just Drop It for Now
TranslationService: Cleaned title result: Chapter 7 Maybe We Should Just Drop It for Now
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 59 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess (referring to Zhu Cixia)
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 7581
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: '《刺客联盟》' (matched pattern: /^[。！？、，]/)
TranslationService: AI parsed 5 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Selective filtering reduced pattern names from 0 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":7,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":6,"chapter_number":8,"target_language":"en"}
Starting translate - Novel ID: 6, Chapters: 8, Language: en
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 485
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第8章 风之旅人
TranslationService: Chapter content length: 6314
TranslationService: getNameDictionary query returned 61 entries for novel 6
TranslationService: Name dictionary entries: 61
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 61 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess (referring to Zhu Cixia)
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Chapter 8: Journey
DeepSeekTranslationService: Cleaned title result: Chapter 8: Journey
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation
TranslationService: Original title response: Chapter 8: Journey
TranslationService: Cleaned title result: Chapter 8: Journey
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 61 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess (referring to Zhu Cixia)
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 《 at position 1599
DeepSeekTranslationService: Restored " → 》 at position 1623
DeepSeekTranslationService: Restored " → 《 at position 1629
DeepSeekTranslationService: Restored " → 》 at position 1653
DeepSeekTranslationService: Restored " → 《 at position 2198
DeepSeekTranslationService: Restored " → 》 at position 2242
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 6314
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: '《风之旅人》' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '灵犀' (matched pattern: /[。！？、，]$/)
TranslationService: AI parsed 3 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Selective filtering reduced pattern names from 0 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":8,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":6,"chapter_number":9,"target_language":"en"}
Starting translate - Novel ID: 6, Chapters: 9, Language: en
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 486
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第9章 完成
TranslationService: Chapter content length: 6641
TranslationService: getNameDictionary query returned 80 entries for novel 6
TranslationService: Name dictionary entries: 80
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 白房子 → White House
DeepSeekTranslationService: Name mapping - 沙漠 → Desert
DeepSeekTranslationService: Name mapping - 地下 → Underground
DeepSeekTranslationService: Name mapping - 神庙 → Temple
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Chapter 9: Completion
DeepSeekTranslationService: Cleaned title result: Chapter 9: Completion
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation
TranslationService: Original title response: Chapter 9: Completion
TranslationService: Cleaned title result: Chapter 9: Completion
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 白房子 → White House
DeepSeekTranslationService: Name mapping - 沙漠 → Desert
DeepSeekTranslationService: Name mapping - 地下 → Underground
DeepSeekTranslationService: Name mapping - 神庙 → Temple
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 6641
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: AI parsed 5 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Selective filtering reduced pattern names from 0 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":9,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":6,"chapter_number":10,"target_language":"en"}
Starting translate - Novel ID: 6, Chapters: 10, Language: en
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 487
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第10章 公主的游戏初体验
TranslationService: Chapter content length: 6093
TranslationService: getNameDictionary query returned 80 entries for novel 6
TranslationService: Name dictionary entries: 80
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 白房子 → White House
DeepSeekTranslationService: Name mapping - 沙漠 → Desert
DeepSeekTranslationService: Name mapping - 地下 → Underground
DeepSeekTranslationService: Name mapping - 神庙 → Temple
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Chapter 10: The Princess's First Gaming Experience
DeepSeekTranslationService: Cleaned title result: Chapter 10: The Princess's First Gaming Experience
TranslationService: DeepSeek translation successful
TranslationService: Original title response: Chapter 10: The Princess's First Gaming Experience
TranslationService: Cleaned title result: Chapter 10: The Princess's First Gaming Experience
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 白房子 → White House
DeepSeekTranslationService: Name mapping - 沙漠 → Desert
DeepSeekTranslationService: Name mapping - 地下 → Underground
DeepSeekTranslationService: Name mapping - 神庙 → Temple
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 6093
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: AI parsed 4 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Selective filtering reduced pattern names from 0 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":10,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":6,"chapter_number":5,"target_language":"en"}
Starting translate - Novel ID: 6, Chapters: 5, Language: en
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 482
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第5章 小梦
TranslationService: Chapter content length: 6834
TranslationService: getNameDictionary query returned 80 entries for novel 6
TranslationService: Name dictionary entries: 80
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 白房子 → White House
DeepSeekTranslationService: Name mapping - 沙漠 → Desert
DeepSeekTranslationService: Name mapping - 地下 → Underground
DeepSeekTranslationService: Name mapping - 神庙 → Temple
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Chapter 5: Xiaomeng
DeepSeekTranslationService: Cleaned title result: Chapter 5: Xiaomeng
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation
TranslationService: Original title response: Chapter 5: Xiaomeng
TranslationService: Cleaned title result: Chapter 5: Xiaomeng
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 白房子 → White House
DeepSeekTranslationService: Name mapping - 沙漠 → Desert
DeepSeekTranslationService: Name mapping - 地下 → Underground
DeepSeekTranslationService: Name mapping - 神庙 → Temple
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 6834
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: AI parsed 4 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Selective filtering reduced pattern names from 0 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":5,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":1,"chapter_number":154,"target_language":"en"}
Starting translate - Novel ID: 1, Chapters: 154, Language: en
TranslationService: Starting chapter translation - Novel ID: 1, Chapter ID: 154
TranslationService: Chapter has chunks, using chunked translation
TranslationService: getNameDictionary query returned 72 entries for novel 1
TranslationService: Name dictionary entries: 72
TranslationService: Name dictionary details:
  - ゼロス → Zeros (using translation)
  - イリス → Iris (using romanization)
  - ルーセリス → Luceris (using romanization)
  - アド → Ado (using romanization)
  - レナ → Rena (using romanization)
  - ジャーネ → Jane (using romanization)
  - ユイ → Yui (using romanization)
  - リサ → Lisa (using romanization)
  - ブリザード・カイザードラゴン → Blizzard Kaiser Dragon (using translation)
  - アルフィア → Alphia (using romanization)
  - アド君 → Ado-kun (using romanization)
  - ゼロスさん → Zeros-san (using romanization)
  - 男性ホルモン増強剤 → Male hormone enhancer (using translation)
  - シャクティ → Shakti (using romanization)
  - ナグリ → Naguri (using romanization)
  - 麗美 → Reimi (using translation)
  - サントール → Santor (using translation)
  - ハンバ土木工業 → Hanba Civil Engineering Company (using translation)
  - 回春の秘薬 → Elixir of rejuvenation (using translation)
  - 性別変換薬 → Gender Transformation Potion (using translation)
  - ジョニー → Johnny (using translation)
  - カイ → Kai (using translation)
  - ツヴェイト → Zweit (using translation)
  - アンジェ → Angie (using translation)
  - ナボさん → Nabo-san (using romanization)
  - ケモさん → Kemo-san (using romanization)
  - テッド → Ted (using translation)
  - シャランラ → Sharanra (using translation)
  - ザボン → Zabon (using translation)
  - 大迫　聡 → Osako Satoshi (using translation)
  - 聡 → Satoshi (using translation)
  - シャーラ → Shara (using translation)
  - イストール魔法学院 → Istor Magic Academy (using translation)
  - 四神 → The Four Gods (using translation)
  - 公爵家 → Duke's Household (using translation)
  - トンカチ工房 → Hammer Workshop (using translation)
  - ソリステア商会 → Solistia Trading Company (using translation)
  - 異端審問部 → Inquisition Department (using translation)
  - メーティス聖法神国 → Metis Holy Kingdom (using translation)
  - ソリステア魔法王国 → Solistia Magic Kingdom (using translation)
  - 鑑定 → Appraisal (using translation)
  - ファイアボール → Fireball (using translation)
  - アンデッドモンスター → Undead Monster (using translation)
  - ボロモロ鳥 → Boromoro Bird (using translation)
  - 空のゴブリン → Goblin of the Sky (using translation)
  - ソード・アンド・ソーサリス → Sword and Sorcery (using translation)
  - アバター → Avatar (using translation)
  - レイド → Raid (using translation)
  - おっさん → Ossan (using romanization)
  - おじさん → Ojisan (using romanization)
  - 親父 → Oyaji (using romanization)
  - おじいさん → Ojiisan (using romanization)
  - グレート・ギヴリオン → Great Givurion (using translation)
  - エビチリ → ebi-chilli (using translation)
  - 魔法薬 → Magic potion (using translation)
  - 呪われた武器 → Cursed weapon (using translation)
  - 灰色ローブ → Gray robe (using translation)
  - 栄養ドリンク → Energy drink (using translation)
  - 女性変換薬 → Female Transformation Potion (using translation)
  - 短時間女性変換薬 → Short-Term Female Transformation Potion (using translation)
  - エクスカリバー → Excalibur (using translation)
  - ポーション → Potion (using translation)
  - 夜のファイト一発 → One Shot Fight at Night (using translation)
  - ゾンビドリンク → Zombie Drink (using translation)
  - リフレッシュ・エナジードリンク 試作９３号 → Refresh Energy Drink Prototype No. 93 (using translation)
  - 威圧 → Intimidation (using translation)
  - ボロモロ鳥のピリ辛揚げ → Spicy Fried Boromoro Bird (using translation)
  - ハイ・エルフ → High Elf (using translation)
  - 騎士 → Knight (using translation)
  - 殲滅者 → Annihilator (using translation)
  - 白の殲滅者 → White Annihilator (using translation)
  - ＰＫ職 → PK (Player Killer) Profession (using translation)
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 72 entries
DeepSeekTranslationService: Name mapping - ゼロス → Zeros
DeepSeekTranslationService: Name mapping - イリス → Iris
DeepSeekTranslationService: Name mapping - ルーセリス → Luceris
DeepSeekTranslationService: Name mapping - アド → Ado
DeepSeekTranslationService: Name mapping - レナ → Rena
DeepSeekTranslationService: Name mapping - ジャーネ → Jane
DeepSeekTranslationService: Name mapping - ユイ → Yui
DeepSeekTranslationService: Name mapping - リサ → Lisa
DeepSeekTranslationService: Name mapping - ブリザード・カイザードラゴン → Blizzard Kaiser Dragon
DeepSeekTranslationService: Name mapping - アルフィア → Alphia
DeepSeekTranslationService: Name mapping - アド君 → Ado-kun
DeepSeekTranslationService: Name mapping - ゼロスさん → Zeros-san
DeepSeekTranslationService: Name mapping - 男性ホルモン増強剤 → Male hormone enhancer
DeepSeekTranslationService: Name mapping - シャクティ → Shakti
DeepSeekTranslationService: Name mapping - ナグリ → Naguri
DeepSeekTranslationService: Name mapping - 麗美 → Reimi
DeepSeekTranslationService: Name mapping - サントール → Santor
DeepSeekTranslationService: Name mapping - ハンバ土木工業 → Hanba Civil Engineering Company
DeepSeekTranslationService: Name mapping - 回春の秘薬 → Elixir of rejuvenation
DeepSeekTranslationService: Name mapping - 性別変換薬 → Gender Transformation Potion
DeepSeekTranslationService: Name mapping - ジョニー → Johnny
DeepSeekTranslationService: Name mapping - カイ → Kai
DeepSeekTranslationService: Name mapping - ツヴェイト → Zweit
DeepSeekTranslationService: Name mapping - アンジェ → Angie
DeepSeekTranslationService: Name mapping - ナボさん → Nabo-san
DeepSeekTranslationService: Name mapping - ケモさん → Kemo-san
DeepSeekTranslationService: Name mapping - テッド → Ted
DeepSeekTranslationService: Name mapping - シャランラ → Sharanra
DeepSeekTranslationService: Name mapping - ザボン → Zabon
DeepSeekTranslationService: Name mapping - 大迫　聡 → Osako Satoshi
DeepSeekTranslationService: Name mapping - 聡 → Satoshi
DeepSeekTranslationService: Name mapping - シャーラ → Shara
DeepSeekTranslationService: Name mapping - イストール魔法学院 → Istor Magic Academy
DeepSeekTranslationService: Name mapping - 四神 → The Four Gods
DeepSeekTranslationService: Name mapping - 公爵家 → Duke's Household
DeepSeekTranslationService: Name mapping - トンカチ工房 → Hammer Workshop
DeepSeekTranslationService: Name mapping - ソリステア商会 → Solistia Trading Company
DeepSeekTranslationService: Name mapping - 異端審問部 → Inquisition Department
DeepSeekTranslationService: Name mapping - メーティス聖法神国 → Metis Holy Kingdom
DeepSeekTranslationService: Name mapping - ソリステア魔法王国 → Solistia Magic Kingdom
DeepSeekTranslationService: Name mapping - 鑑定 → Appraisal
DeepSeekTranslationService: Name mapping - ファイアボール → Fireball
DeepSeekTranslationService: Name mapping - アンデッドモンスター → Undead Monster
DeepSeekTranslationService: Name mapping - ボロモロ鳥 → Boromoro Bird
DeepSeekTranslationService: Name mapping - 空のゴブリン → Goblin of the Sky
DeepSeekTranslationService: Name mapping - ソード・アンド・ソーサリス → Sword and Sorcery
DeepSeekTranslationService: Name mapping - アバター → Avatar
DeepSeekTranslationService: Name mapping - レイド → Raid
DeepSeekTranslationService: Name mapping - おっさん → Ossan
DeepSeekTranslationService: Name mapping - おじさん → Ojisan
DeepSeekTranslationService: Name mapping - 親父 → Oyaji
DeepSeekTranslationService: Name mapping - おじいさん → Ojiisan
DeepSeekTranslationService: Name mapping - グレート・ギヴリオン → Great Givurion
DeepSeekTranslationService: Name mapping - エビチリ → ebi-chilli
DeepSeekTranslationService: Name mapping - 魔法薬 → Magic potion
DeepSeekTranslationService: Name mapping - 呪われた武器 → Cursed weapon
DeepSeekTranslationService: Name mapping - 灰色ローブ → Gray robe
DeepSeekTranslationService: Name mapping - 栄養ドリンク → Energy drink
DeepSeekTranslationService: Name mapping - 女性変換薬 → Female Transformation Potion
DeepSeekTranslationService: Name mapping - 短時間女性変換薬 → Short-Term Female Transformation Potion
DeepSeekTranslationService: Name mapping - エクスカリバー → Excalibur
DeepSeekTranslationService: Name mapping - ポーション → Potion
DeepSeekTranslationService: Name mapping - 夜のファイト一発 → One Shot Fight at Night
DeepSeekTranslationService: Name mapping - ゾンビドリンク → Zombie Drink
DeepSeekTranslationService: Name mapping - リフレッシュ・エナジードリンク 試作９３号 → Refresh Energy Drink Prototype No. 93
DeepSeekTranslationService: Name mapping - 威圧 → Intimidation
DeepSeekTranslationService: Name mapping - ボロモロ鳥のピリ辛揚げ → Spicy Fried Boromoro Bird
DeepSeekTranslationService: Name mapping - ハイ・エルフ → High Elf
DeepSeekTranslationService: Name mapping - 騎士 → Knight
DeepSeekTranslationService: Name mapping - 殲滅者 → Annihilator
DeepSeekTranslationService: Name mapping - 白の殲滅者 → White Annihilator
DeepSeekTranslationService: Name mapping - ＰＫ職 → PK (Player Killer) Profession
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Honorific_Marker_0 began preparing for the sibling fight.
DeepSeekTranslationService: Cleaned title result: Honorific_Marker_0 began preparing for the sibling fight.
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation, Significant punctuation loss detected
TranslationService: Original title response: Honorific_Marker_0 began preparing for the sibling fight.
TranslationService: Cleaned title result: Honorific_Marker_0 began preparing for the sibling fight.
TranslationService: Found 3 chunks to translate
TranslationService: Translating chunk 1/{3}
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 72 entries
DeepSeekTranslationService: Name mapping - ゼロス → Zeros
DeepSeekTranslationService: Name mapping - イリス → Iris
DeepSeekTranslationService: Name mapping - ルーセリス → Luceris
DeepSeekTranslationService: Name mapping - アド → Ado
DeepSeekTranslationService: Name mapping - レナ → Rena
DeepSeekTranslationService: Name mapping - ジャーネ → Jane
DeepSeekTranslationService: Name mapping - ユイ → Yui
DeepSeekTranslationService: Name mapping - リサ → Lisa
DeepSeekTranslationService: Name mapping - ブリザード・カイザードラゴン → Blizzard Kaiser Dragon
DeepSeekTranslationService: Name mapping - アルフィア → Alphia
DeepSeekTranslationService: Name mapping - アド君 → Ado-kun
DeepSeekTranslationService: Name mapping - ゼロスさん → Zeros-san
DeepSeekTranslationService: Name mapping - 男性ホルモン増強剤 → Male hormone enhancer
DeepSeekTranslationService: Name mapping - シャクティ → Shakti
DeepSeekTranslationService: Name mapping - ナグリ → Naguri
DeepSeekTranslationService: Name mapping - 麗美 → Reimi
DeepSeekTranslationService: Name mapping - サントール → Santor
DeepSeekTranslationService: Name mapping - ハンバ土木工業 → Hanba Civil Engineering Company
DeepSeekTranslationService: Name mapping - 回春の秘薬 → Elixir of rejuvenation
DeepSeekTranslationService: Name mapping - 性別変換薬 → Gender Transformation Potion
DeepSeekTranslationService: Name mapping - ジョニー → Johnny
DeepSeekTranslationService: Name mapping - カイ → Kai
DeepSeekTranslationService: Name mapping - ツヴェイト → Zweit
DeepSeekTranslationService: Name mapping - アンジェ → Angie
DeepSeekTranslationService: Name mapping - ナボさん → Nabo-san
DeepSeekTranslationService: Name mapping - ケモさん → Kemo-san
DeepSeekTranslationService: Name mapping - テッド → Ted
DeepSeekTranslationService: Name mapping - シャランラ → Sharanra
DeepSeekTranslationService: Name mapping - ザボン → Zabon
DeepSeekTranslationService: Name mapping - 大迫　聡 → Osako Satoshi
DeepSeekTranslationService: Name mapping - 聡 → Satoshi
DeepSeekTranslationService: Name mapping - シャーラ → Shara
DeepSeekTranslationService: Name mapping - イストール魔法学院 → Istor Magic Academy
DeepSeekTranslationService: Name mapping - 四神 → The Four Gods
DeepSeekTranslationService: Name mapping - 公爵家 → Duke's Household
DeepSeekTranslationService: Name mapping - トンカチ工房 → Hammer Workshop
DeepSeekTranslationService: Name mapping - ソリステア商会 → Solistia Trading Company
DeepSeekTranslationService: Name mapping - 異端審問部 → Inquisition Department
DeepSeekTranslationService: Name mapping - メーティス聖法神国 → Metis Holy Kingdom
DeepSeekTranslationService: Name mapping - ソリステア魔法王国 → Solistia Magic Kingdom
DeepSeekTranslationService: Name mapping - 鑑定 → Appraisal
DeepSeekTranslationService: Name mapping - ファイアボール → Fireball
DeepSeekTranslationService: Name mapping - アンデッドモンスター → Undead Monster
DeepSeekTranslationService: Name mapping - ボロモロ鳥 → Boromoro Bird
DeepSeekTranslationService: Name mapping - 空のゴブリン → Goblin of the Sky
DeepSeekTranslationService: Name mapping - ソード・アンド・ソーサリス → Sword and Sorcery
DeepSeekTranslationService: Name mapping - アバター → Avatar
DeepSeekTranslationService: Name mapping - レイド → Raid
DeepSeekTranslationService: Name mapping - おっさん → Ossan
DeepSeekTranslationService: Name mapping - おじさん → Ojisan
DeepSeekTranslationService: Name mapping - 親父 → Oyaji
DeepSeekTranslationService: Name mapping - おじいさん → Ojiisan
DeepSeekTranslationService: Name mapping - グレート・ギヴリオン → Great Givurion
DeepSeekTranslationService: Name mapping - エビチリ → ebi-chilli
DeepSeekTranslationService: Name mapping - 魔法薬 → Magic potion
DeepSeekTranslationService: Name mapping - 呪われた武器 → Cursed weapon
DeepSeekTranslationService: Name mapping - 灰色ローブ → Gray robe
DeepSeekTranslationService: Name mapping - 栄養ドリンク → Energy drink
DeepSeekTranslationService: Name mapping - 女性変換薬 → Female Transformation Potion
DeepSeekTranslationService: Name mapping - 短時間女性変換薬 → Short-Term Female Transformation Potion
DeepSeekTranslationService: Name mapping - エクスカリバー → Excalibur
DeepSeekTranslationService: Name mapping - ポーション → Potion
DeepSeekTranslationService: Name mapping - 夜のファイト一発 → One Shot Fight at Night
DeepSeekTranslationService: Name mapping - ゾンビドリンク → Zombie Drink
DeepSeekTranslationService: Name mapping - リフレッシュ・エナジードリンク 試作９３号 → Refresh Energy Drink Prototype No. 93
DeepSeekTranslationService: Name mapping - 威圧 → Intimidation
DeepSeekTranslationService: Name mapping - ボロモロ鳥のピリ辛揚げ → Spicy Fried Boromoro Bird
DeepSeekTranslationService: Name mapping - ハイ・エルフ → High Elf
DeepSeekTranslationService: Name mapping - 騎士 → Knight
DeepSeekTranslationService: Name mapping - 殲滅者 → Annihilator
DeepSeekTranslationService: Name mapping - 白の殲滅者 → White Annihilator
DeepSeekTranslationService: Name mapping - ＰＫ職 → PK (Player Killer) Profession
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 『 at position 496
DeepSeekTranslationService: Restored " → 』 at position 512
DeepSeekTranslationService: Restored " → 「 at position 517
DeepSeekTranslationService: Restored " → 」 at position 559
DeepSeekTranslationService: Restored " → 「 at position 564
DeepSeekTranslationService: Restored " → 」 at position 652
DeepSeekTranslationService: Restored " → 「 at position 657
DeepSeekTranslationService: Restored " → 」 at position 708
DeepSeekTranslationService: Restored " → 「 at position 1032
DeepSeekTranslationService: Restored " → 」 at position 1081
DeepSeekTranslationService: Restored " → 「 at position 1086
DeepSeekTranslationService: Restored " → 」 at position 1203
DeepSeekTranslationService: Restored " → 「 at position 1208
DeepSeekTranslationService: Restored " → 」 at position 1328
DeepSeekTranslationService: Restored " → 「 at position 1333
DeepSeekTranslationService: Restored " → 」 at position 1393
DeepSeekTranslationService: Restored " → 「 at position 1398
DeepSeekTranslationService: Restored " → 」 at position 1586
DeepSeekTranslationService: Restored " → 「 at position 1976
DeepSeekTranslationService: Restored " → 」 at position 2017
DeepSeekTranslationService: Restored " → 「 at position 2022
DeepSeekTranslationService: Restored " → 」 at position 2203
DeepSeekTranslationService: Restored " → 「 at position 2208
DeepSeekTranslationService: Restored " → 」 at position 2328
DeepSeekTranslationService: Restored " → 「 at position 2333
DeepSeekTranslationService: Restored " → 」 at position 2381
DeepSeekTranslationService: Restored " → 「 at position 2386
DeepSeekTranslationService: Restored " → 」 at position 2463
DeepSeekTranslationService: Restored " → 「 at position 2667
DeepSeekTranslationService: Restored " → 」 at position 2754
DeepSeekTranslationService: Restored " → 「 at position 2759
DeepSeekTranslationService: Restored " → 」 at position 2791
DeepSeekTranslationService: Restored " → 「 at position 2796
DeepSeekTranslationService: Restored " → 」 at position 2884
DeepSeekTranslationService: Restored " → 「 at position 2889
DeepSeekTranslationService: Restored " → 」 at position 2912
DeepSeekTranslationService: Restored " → 「 at position 3165
DeepSeekTranslationService: Restored " → 」 at position 3278
DeepSeekTranslationService: Restored " → 「 at position 3283
DeepSeekTranslationService: Restored " → 」 at position 3454
DeepSeekTranslationService: Restored " → 「 at position 3459
DeepSeekTranslationService: Restored " → 」 at position 3508
DeepSeekTranslationService: Restored " → 「 at position 3513
DeepSeekTranslationService: Restored " → 」 at position 3575
DeepSeekTranslationService: Restored " → 「 at position 3580
DeepSeekTranslationService: Restored " → 」 at position 3598
DeepSeekTranslationService: Restored " → 「 at position 3973
DeepSeekTranslationService: Restored " → 「 at position 4029
DeepSeekTranslationService: Restored " → 「 at position 4034
DeepSeekTranslationService: Restored " → 「 at position 4105
DeepSeekTranslationService: Restored " → 」 at position 4110
DeepSeekTranslationService: Restored " → 」 at position 4154
DeepSeekTranslationService: Restored " → 」 at position 4159
DeepSeekTranslationService: Restored " → 」 at position 4199
DeepSeekTranslationService: Restored " → 「 at position 4204
DeepSeekTranslationService: Restored " → 」 at position 4341
DeepSeekTranslationService: Restored " → 「 at position 4589
DeepSeekTranslationService: Restored " → 」 at position 4642
DeepSeekTranslationService: Restored " → 「 at position 4647
DeepSeekTranslationService: Restored " → 」 at position 4673
DeepSeekTranslationService: Restored " → 「 at position 4678
DeepSeekTranslationService: Restored " → 」 at position 4698
DeepSeekTranslationService: Restored " → 「 at position 4703
DeepSeekTranslationService: Restored " → 」 at position 4738
DeepSeekTranslationService: Restored " → 『 at position 4743
DeepSeekTranslationService: Restored " → 』 at position 4753
DeepSeekTranslationService: Restored " → 「 at position 4758
DeepSeekTranslationService: Restored " → 」 at position 4844
DeepSeekTranslationService: Restored " → 「 at position 5501
DeepSeekTranslationService: Restored " → 」 at position 5632
DeepSeekTranslationService: Restored " → 「 at position 5637
DeepSeekTranslationService: Restored " → 」 at position 5658
DeepSeekTranslationService: Restored " → 「 at position 5663
DeepSeekTranslationService: Restored " → 」 at position 5861
DeepSeekTranslationService: Restored " → 「 at position 5866
DeepSeekTranslationService: Restored " → 」 at position 5988
DeepSeekTranslationService: Restored " → 「 at position 6496
DeepSeekTranslationService: Restored " → 」 at position 6581
DeepSeekTranslationService: Restored " → 「 at position 6586
DeepSeekTranslationService: Restored " → 」 at position 6712
DeepSeekTranslationService: Restored " → 「 at position 6717
DeepSeekTranslationService: Restored " → 「 at position 6774
DeepSeekTranslationService: Restored " → 「 at position 6779
DeepSeekTranslationService: Restored " → 「 at position 6849
DeepSeekTranslationService: Restored " → 「 at position 6959
DeepSeekTranslationService: Restored " → 「 at position 6992
DeepSeekTranslationService: Restored " → 「 at position 6997
DeepSeekTranslationService: Restored " → 「 at position 7031
DeepSeekTranslationService: Restored " → 」 at position 7036
DeepSeekTranslationService: Restored " → 」 at position 7164
DeepSeekTranslationService: Restored " → 」 at position 7169
DeepSeekTranslationService: Restored " → 」 at position 7215
DeepSeekTranslationService: Restored " → 」 at position 7220
DeepSeekTranslationService: Restored " → 」 at position 7239
DeepSeekTranslationService: Restored " → 」 at position 7244
DeepSeekTranslationService: Restored " → 」 at position 7319
DeepSeekTranslationService: Restored " → 」 at position 7324
DeepSeekTranslationService: Restored " → 「 at position 7466
DeepSeekTranslationService: Restored " → 」 at position 7471
DeepSeekTranslationService: Restored " → 「 at position 7529
DeepSeekTranslationService: Restored " → 」 at position 8506
DeepSeekTranslationService: Restored " → 「 at position 8717
DeepSeekTranslationService: Restored " → 」 at position 8722
DeepSeekTranslationService: Restored " → 「 at position 8800
DeepSeekTranslationService: Restored " → 」 at position 8805
DeepSeekTranslationService: Restored " → 「 at position 8827
DeepSeekTranslationService: Restored " → 」 at position 8832
DeepSeekTranslationService: Restored " → 「 at position 8860
DeepSeekTranslationService: Restored " → 」 at position 8865
DeepSeekTranslationService: Restored " → 「 at position 8909
DeepSeekTranslationService: Restored " → 」 at position 8914
DeepSeekTranslationService: Restored " → 「 at position 8956
DeepSeekTranslationService: Restored " → 」 at position 8961
DeepSeekTranslationService: Restored " → 「 at position 8997
DeepSeekTranslationService: Restored " → 」 at position 9430
DeepSeekTranslationService: Restored " → 「 at position 9446
DeepSeekTranslationService: Restored " → 」 at position 9451
DeepSeekTranslationService: Restored " → 「 at position 9464
DeepSeekTranslationService: Restored " → 」 at position 9469
DeepSeekTranslationService: Restored " → 「 at position 9481
DeepSeekTranslationService: Restored " → 」 at position 9486
DeepSeekTranslationService: Restored " → 「 at position 9513
DeepSeekTranslationService: Restored " → 」 at position 9518
DeepSeekTranslationService: Restored " → 「 at position 9536
DeepSeekTranslationService: Restored " → 」 at position 9541
DeepSeekTranslationService: Restored " → 「 at position 9584
DeepSeekTranslationService: Restored " → 」 at position 9589
DeepSeekTranslationService: Restored " → 「 at position 9709
DeepSeekTranslationService: Restored " → 」 at position 9714
DeepSeekTranslationService: Restored " → 「 at position 9796
DeepSeekTranslationService: Restored " → 」 at position 9801
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Paragraph structure significantly changed: 153 → 137, Significant punctuation loss detected
TranslationService: Extracting names from chunk 1
TranslationService: Starting AI-based name extraction from text length: 14398
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: 'ゼロス' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ジョニー君' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ルーセリス' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'イリス' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'レナ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ジャーネ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'カエデ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '【女性変換薬】' (matched pattern: /^[。！？、，]/)
TranslationService: AI parsed 0 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Selective filtering reduced pattern names from 10 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names in chunk 1
TranslationService: Translating chunk 2/{3}
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 72 entries
DeepSeekTranslationService: Name mapping - ゼロス → Zeros
DeepSeekTranslationService: Name mapping - イリス → Iris
DeepSeekTranslationService: Name mapping - ルーセリス → Luceris
DeepSeekTranslationService: Name mapping - アド → Ado
DeepSeekTranslationService: Name mapping - レナ → Rena
DeepSeekTranslationService: Name mapping - ジャーネ → Jane
DeepSeekTranslationService: Name mapping - ユイ → Yui
DeepSeekTranslationService: Name mapping - リサ → Lisa
DeepSeekTranslationService: Name mapping - ブリザード・カイザードラゴン → Blizzard Kaiser Dragon
DeepSeekTranslationService: Name mapping - アルフィア → Alphia
DeepSeekTranslationService: Name mapping - アド君 → Ado-kun
DeepSeekTranslationService: Name mapping - ゼロスさん → Zeros-san
DeepSeekTranslationService: Name mapping - 男性ホルモン増強剤 → Male hormone enhancer
DeepSeekTranslationService: Name mapping - シャクティ → Shakti
DeepSeekTranslationService: Name mapping - ナグリ → Naguri
DeepSeekTranslationService: Name mapping - 麗美 → Reimi
DeepSeekTranslationService: Name mapping - サントール → Santor
DeepSeekTranslationService: Name mapping - ハンバ土木工業 → Hanba Civil Engineering Company
DeepSeekTranslationService: Name mapping - 回春の秘薬 → Elixir of rejuvenation
DeepSeekTranslationService: Name mapping - 性別変換薬 → Gender Transformation Potion
DeepSeekTranslationService: Name mapping - ジョニー → Johnny
DeepSeekTranslationService: Name mapping - カイ → Kai
DeepSeekTranslationService: Name mapping - ツヴェイト → Zweit
DeepSeekTranslationService: Name mapping - アンジェ → Angie
DeepSeekTranslationService: Name mapping - ナボさん → Nabo-san
DeepSeekTranslationService: Name mapping - ケモさん → Kemo-san
DeepSeekTranslationService: Name mapping - テッド → Ted
DeepSeekTranslationService: Name mapping - シャランラ → Sharanra
DeepSeekTranslationService: Name mapping - ザボン → Zabon
DeepSeekTranslationService: Name mapping - 大迫　聡 → Osako Satoshi
DeepSeekTranslationService: Name mapping - 聡 → Satoshi
DeepSeekTranslationService: Name mapping - シャーラ → Shara
DeepSeekTranslationService: Name mapping - イストール魔法学院 → Istor Magic Academy
DeepSeekTranslationService: Name mapping - 四神 → The Four Gods
DeepSeekTranslationService: Name mapping - 公爵家 → Duke's Household
DeepSeekTranslationService: Name mapping - トンカチ工房 → Hammer Workshop
DeepSeekTranslationService: Name mapping - ソリステア商会 → Solistia Trading Company
DeepSeekTranslationService: Name mapping - 異端審問部 → Inquisition Department
DeepSeekTranslationService: Name mapping - メーティス聖法神国 → Metis Holy Kingdom
DeepSeekTranslationService: Name mapping - ソリステア魔法王国 → Solistia Magic Kingdom
DeepSeekTranslationService: Name mapping - 鑑定 → Appraisal
DeepSeekTranslationService: Name mapping - ファイアボール → Fireball
DeepSeekTranslationService: Name mapping - アンデッドモンスター → Undead Monster
DeepSeekTranslationService: Name mapping - ボロモロ鳥 → Boromoro Bird
DeepSeekTranslationService: Name mapping - 空のゴブリン → Goblin of the Sky
DeepSeekTranslationService: Name mapping - ソード・アンド・ソーサリス → Sword and Sorcery
DeepSeekTranslationService: Name mapping - アバター → Avatar
DeepSeekTranslationService: Name mapping - レイド → Raid
DeepSeekTranslationService: Name mapping - おっさん → Ossan
DeepSeekTranslationService: Name mapping - おじさん → Ojisan
DeepSeekTranslationService: Name mapping - 親父 → Oyaji
DeepSeekTranslationService: Name mapping - おじいさん → Ojiisan
DeepSeekTranslationService: Name mapping - グレート・ギヴリオン → Great Givurion
DeepSeekTranslationService: Name mapping - エビチリ → ebi-chilli
DeepSeekTranslationService: Name mapping - 魔法薬 → Magic potion
DeepSeekTranslationService: Name mapping - 呪われた武器 → Cursed weapon
DeepSeekTranslationService: Name mapping - 灰色ローブ → Gray robe
DeepSeekTranslationService: Name mapping - 栄養ドリンク → Energy drink
DeepSeekTranslationService: Name mapping - 女性変換薬 → Female Transformation Potion
DeepSeekTranslationService: Name mapping - 短時間女性変換薬 → Short-Term Female Transformation Potion
DeepSeekTranslationService: Name mapping - エクスカリバー → Excalibur
DeepSeekTranslationService: Name mapping - ポーション → Potion
DeepSeekTranslationService: Name mapping - 夜のファイト一発 → One Shot Fight at Night
DeepSeekTranslationService: Name mapping - ゾンビドリンク → Zombie Drink
DeepSeekTranslationService: Name mapping - リフレッシュ・エナジードリンク 試作９３号 → Refresh Energy Drink Prototype No. 93
DeepSeekTranslationService: Name mapping - 威圧 → Intimidation
DeepSeekTranslationService: Name mapping - ボロモロ鳥のピリ辛揚げ → Spicy Fried Boromoro Bird
DeepSeekTranslationService: Name mapping - ハイ・エルフ → High Elf
DeepSeekTranslationService: Name mapping - 騎士 → Knight
DeepSeekTranslationService: Name mapping - 殲滅者 → Annihilator
DeepSeekTranslationService: Name mapping - 白の殲滅者 → White Annihilator
DeepSeekTranslationService: Name mapping - ＰＫ職 → PK (Player Killer) Profession
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Non-retryable error, stopping retries
TranslationService: DeepSeek failed, trying Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Extracting names from chunk 2
TranslationService: Starting AI-based name extraction from text length: 12901
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out too short name: '聡'
TranslationService: Filtered out false positive: 'ルーセリス' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ジャーネ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'レナ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ゼロス' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ウーケイ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '【回春の秘薬】' (matched pattern: /^[。！？、，]/)
TranslationService: AI parsed 1 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Retryable error detected: HTTP error: 429
TranslationService: Switching to fallback model due to rate limited error
TranslationService: Attempt 2 using fallback (2.0-flash) model
TranslationService: SUCCESS with fallback model after primary failed
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Retryable error detected: HTTP error: 429
TranslationService: Switching to fallback model due to rate limited error
TranslationService: Attempt 2 using fallback (2.0-flash) model
TranslationService: SUCCESS with fallback model after primary failed
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Retryable error detected: HTTP error: 429
TranslationService: Switching to fallback model due to rate limited error
TranslationService: Attempt 2 using fallback (2.0-flash) model
TranslationService: SUCCESS with fallback model after primary failed
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Retryable error detected: HTTP error: 429
TranslationService: Switching to fallback model due to rate limited error
TranslationService: Attempt 2 using fallback (2.0-flash) model
TranslationService: SUCCESS with fallback model after primary failed
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Retryable error detected: HTTP error: 429
TranslationService: Switching to fallback model due to rate limited error
TranslationService: Attempt 2 using fallback (2.0-flash) model
TranslationService: SUCCESS with fallback model after primary failed
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Retryable error detected: HTTP error: 429
TranslationService: Switching to fallback model due to rate limited error
TranslationService: Attempt 2 using fallback (2.0-flash) model
TranslationService: SUCCESS with fallback model after primary failed
TranslationService: Selective filtering reduced pattern names from 10 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names in chunk 2
TranslationService: Translating chunk 3/{3}
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 72 entries
DeepSeekTranslationService: Name mapping - ゼロス → Zeros
DeepSeekTranslationService: Name mapping - イリス → Iris
DeepSeekTranslationService: Name mapping - ルーセリス → Luceris
DeepSeekTranslationService: Name mapping - アド → Ado
DeepSeekTranslationService: Name mapping - レナ → Rena
DeepSeekTranslationService: Name mapping - ジャーネ → Jane
DeepSeekTranslationService: Name mapping - ユイ → Yui
DeepSeekTranslationService: Name mapping - リサ → Lisa
DeepSeekTranslationService: Name mapping - ブリザード・カイザードラゴン → Blizzard Kaiser Dragon
DeepSeekTranslationService: Name mapping - アルフィア → Alphia
DeepSeekTranslationService: Name mapping - アド君 → Ado-kun
DeepSeekTranslationService: Name mapping - ゼロスさん → Zeros-san
DeepSeekTranslationService: Name mapping - 男性ホルモン増強剤 → Male hormone enhancer
DeepSeekTranslationService: Name mapping - シャクティ → Shakti
DeepSeekTranslationService: Name mapping - ナグリ → Naguri
DeepSeekTranslationService: Name mapping - 麗美 → Reimi
DeepSeekTranslationService: Name mapping - サントール → Santor
DeepSeekTranslationService: Name mapping - ハンバ土木工業 → Hanba Civil Engineering Company
DeepSeekTranslationService: Name mapping - 回春の秘薬 → Elixir of rejuvenation
DeepSeekTranslationService: Name mapping - 性別変換薬 → Gender Transformation Potion
DeepSeekTranslationService: Name mapping - ジョニー → Johnny
DeepSeekTranslationService: Name mapping - カイ → Kai
DeepSeekTranslationService: Name mapping - ツヴェイト → Zweit
DeepSeekTranslationService: Name mapping - アンジェ → Angie
DeepSeekTranslationService: Name mapping - ナボさん → Nabo-san
DeepSeekTranslationService: Name mapping - ケモさん → Kemo-san
DeepSeekTranslationService: Name mapping - テッド → Ted
DeepSeekTranslationService: Name mapping - シャランラ → Sharanra
DeepSeekTranslationService: Name mapping - ザボン → Zabon
DeepSeekTranslationService: Name mapping - 大迫　聡 → Osako Satoshi
DeepSeekTranslationService: Name mapping - 聡 → Satoshi
DeepSeekTranslationService: Name mapping - シャーラ → Shara
DeepSeekTranslationService: Name mapping - イストール魔法学院 → Istor Magic Academy
DeepSeekTranslationService: Name mapping - 四神 → The Four Gods
DeepSeekTranslationService: Name mapping - 公爵家 → Duke's Household
DeepSeekTranslationService: Name mapping - トンカチ工房 → Hammer Workshop
DeepSeekTranslationService: Name mapping - ソリステア商会 → Solistia Trading Company
DeepSeekTranslationService: Name mapping - 異端審問部 → Inquisition Department
DeepSeekTranslationService: Name mapping - メーティス聖法神国 → Metis Holy Kingdom
DeepSeekTranslationService: Name mapping - ソリステア魔法王国 → Solistia Magic Kingdom
DeepSeekTranslationService: Name mapping - 鑑定 → Appraisal
DeepSeekTranslationService: Name mapping - ファイアボール → Fireball
DeepSeekTranslationService: Name mapping - アンデッドモンスター → Undead Monster
DeepSeekTranslationService: Name mapping - ボロモロ鳥 → Boromoro Bird
DeepSeekTranslationService: Name mapping - 空のゴブリン → Goblin of the Sky
DeepSeekTranslationService: Name mapping - ソード・アンド・ソーサリス → Sword and Sorcery
DeepSeekTranslationService: Name mapping - アバター → Avatar
DeepSeekTranslationService: Name mapping - レイド → Raid
DeepSeekTranslationService: Name mapping - おっさん → Ossan
DeepSeekTranslationService: Name mapping - おじさん → Ojisan
DeepSeekTranslationService: Name mapping - 親父 → Oyaji
DeepSeekTranslationService: Name mapping - おじいさん → Ojiisan
DeepSeekTranslationService: Name mapping - グレート・ギヴリオン → Great Givurion
DeepSeekTranslationService: Name mapping - エビチリ → ebi-chilli
DeepSeekTranslationService: Name mapping - 魔法薬 → Magic potion
DeepSeekTranslationService: Name mapping - 呪われた武器 → Cursed weapon
DeepSeekTranslationService: Name mapping - 灰色ローブ → Gray robe
DeepSeekTranslationService: Name mapping - 栄養ドリンク → Energy drink
DeepSeekTranslationService: Name mapping - 女性変換薬 → Female Transformation Potion
DeepSeekTranslationService: Name mapping - 短時間女性変換薬 → Short-Term Female Transformation Potion
DeepSeekTranslationService: Name mapping - エクスカリバー → Excalibur
DeepSeekTranslationService: Name mapping - ポーション → Potion
DeepSeekTranslationService: Name mapping - 夜のファイト一発 → One Shot Fight at Night
DeepSeekTranslationService: Name mapping - ゾンビドリンク → Zombie Drink
DeepSeekTranslationService: Name mapping - リフレッシュ・エナジードリンク 試作９３号 → Refresh Energy Drink Prototype No. 93
DeepSeekTranslationService: Name mapping - 威圧 → Intimidation
DeepSeekTranslationService: Name mapping - ボロモロ鳥のピリ辛揚げ → Spicy Fried Boromoro Bird
DeepSeekTranslationService: Name mapping - ハイ・エルフ → High Elf
DeepSeekTranslationService: Name mapping - 騎士 → Knight
DeepSeekTranslationService: Name mapping - 殲滅者 → Annihilator
DeepSeekTranslationService: Name mapping - 白の殲滅者 → White Annihilator
DeepSeekTranslationService: Name mapping - ＰＫ職 → PK (Player Killer) Profession
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 「 at position 266
DeepSeekTranslationService: Restored " → 」 at position 324
DeepSeekTranslationService: Restored " → 「 at position 329
DeepSeekTranslationService: Restored " → 」 at position 585
DeepSeekTranslationService: Restored " → 「 at position 590
DeepSeekTranslationService: Restored " → 」 at position 838
DeepSeekTranslationService: Restored " → 「 at position 843
DeepSeekTranslationService: Restored " → 」 at position 928
DeepSeekTranslationService: Restored " → 「 at position 933
DeepSeekTranslationService: Restored " → 」 at position 1113
DeepSeekTranslationService: Restored " → 「 at position 1118
DeepSeekTranslationService: Restored " → 」 at position 1228
DeepSeekTranslationService: Restored " → 「 at position 1233
DeepSeekTranslationService: Restored " → 」 at position 1350
DeepSeekTranslationService: Restored " → 『 at position 2011
DeepSeekTranslationService: Restored " → 』 at position 2214
DeepSeekTranslationService: Restored " → 「 at position 2219
DeepSeekTranslationService: Restored " → 」 at position 2269
DeepSeekTranslationService: Restored " → 「 at position 2274
DeepSeekTranslationService: Restored " → 」 at position 2395
DeepSeekTranslationService: Restored " → 「 at position 2400
DeepSeekTranslationService: Restored " → 」 at position 2525
DeepSeekTranslationService: Restored " → 「 at position 2530
DeepSeekTranslationService: Restored " → 」 at position 2626
DeepSeekTranslationService: Restored " → 「 at position 2631
DeepSeekTranslationService: Restored " → 」 at position 2716
DeepSeekTranslationService: Restored " → 「 at position 2799
DeepSeekTranslationService: Restored " → 」 at position 2855
DeepSeekTranslationService: Restored ' → 「 at position 279
DeepSeekTranslationService: Restored ' → 」 at position 399
DeepSeekTranslationService: Restored ' → 『 at position 459
DeepSeekTranslationService: Restored ' → 』 at position 510
DeepSeekTranslationService: Restored ' → 『 at position 602
DeepSeekTranslationService: Restored ' → 』 at position 835
DeepSeekTranslationService: Restored ' → 『 at position 846
DeepSeekTranslationService: Restored ' → 』 at position 903
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Paragraph structure significantly changed: 51 → 44, Significant punctuation loss detected
TranslationService: Extracting names from chunk 3
TranslationService: Starting AI-based name extraction from text length: 5665
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: 'ゼロス' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'アド' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'ユイ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: 'シャランラ' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out too short name: '聡'
TranslationService: Filtered out false positive: '【ソード・アンド・ソーサリス】' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '【シャドウ・ダイブ】' (matched pattern: /^[。！？、，]/)
TranslationService: AI parsed 1 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Selective filtering reduced pattern names from 10 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names in chunk 3
TranslationService: Reassembling 3 chunks with enhanced validation
TranslationService: Content reassembly complete and validated, final length: 25198 characters
TranslationService: All chunks translated and reassembled successfully
TranslationService: Skipping name extraction for chunked translation (already done incrementally)
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":154,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
Parsed input: {"novel_id":6,"chapter_number":6,"target_language":"en"}
Starting translate - Novel ID: 6, Chapters: 6, Language: en
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 483
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 开始做游戏
TranslationService: Chapter content length: 6701
TranslationService: getNameDictionary query returned 80 entries for novel 6
TranslationService: Name dictionary entries: 80
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 白房子 → White House
DeepSeekTranslationService: Name mapping - 沙漠 → Desert
DeepSeekTranslationService: Name mapping - 地下 → Underground
DeepSeekTranslationService: Name mapping - 神庙 → Temple
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Start Making Games
DeepSeekTranslationService: Cleaned title result: Start Making Games
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation, Significant punctuation loss detected
TranslationService: Original title response: Start Making Games
TranslationService: Cleaned title result: Start Making Games
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 白房子 → White House
DeepSeekTranslationService: Name mapping - 沙漠 → Desert
DeepSeekTranslationService: Name mapping - 地下 → Underground
DeepSeekTranslationService: Name mapping - 神庙 → Temple
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
Parsed input: {"novel_id":6,"chapter_number":6,"target_language":"en"}
Starting translate - Novel ID: 6, Chapters: 6, Language: en
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 483
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 开始做游戏
TranslationService: Chapter content length: 6701
TranslationService: getNameDictionary query returned 80 entries for novel 6
TranslationService: Name dictionary entries: 80
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 白房子 → White House
DeepSeekTranslationService: Name mapping - 沙漠 → Desert
DeepSeekTranslationService: Name mapping - 地下 → Underground
DeepSeekTranslationService: Name mapping - 神庙 → Temple
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Start Making Games
DeepSeekTranslationService: Cleaned title result: Start Making Games
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation, Significant punctuation loss detected
TranslationService: Original title response: Start Making Games
TranslationService: Cleaned title result: Start Making Games
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 白房子 → White House
DeepSeekTranslationService: Name mapping - 沙漠 → Desert
DeepSeekTranslationService: Name mapping - 地下 → Underground
DeepSeekTranslationService: Name mapping - 神庙 → Temple
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Restored " → 《 at position 0
DeepSeekTranslationService: Restored " → 》 at position 12
DeepSeekTranslationService: Restored " → 《 at position 77
DeepSeekTranslationService: Restored " → 》 at position 175
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 6701
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Filtered out false positive: '《刺客联盟》' (matched pattern: /^[。！？、，]/)
TranslationService: Filtered out false positive: '《通缉令》' (matched pattern: /^[。！？、，]/)
TranslationService: AI parsed 5 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Selective filtering reduced pattern names from 0 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
Translation result: {"success":true,"total_chapters":1,"successful_translations":1,"failed_translations":0,"results":[{"chapter_number":6,"success":true,"error":null}],"message":"All 1 chapters translated successfully"}
