<?php
/**
 * 69书吧 (69shuba.cx) Crawler
 * Novel Translation Application
 */

class Shuba69Crawler extends BaseCrawler {

    private const BASE_URL = 'https://69shuba.cx';
    private const ALTERNATIVE_URLS = [
        'https://69shu.com',
        'https://69shuba.com',
        'https://www.69shu.com',
        'https://www.69shuba.com'
    ];
    
    /**
     * Validate 69书吧 URL
     */
    protected function validateUrl(string $url): bool {
        // Support multiple URL patterns for 69shuba
        return preg_match('/(69shuba\.cx|69shu\.com|69shuba\.com)\/(book|txt)\/\d+/', $url) === 1;
    }
    
    /**
     * Get novel information from 69书吧
     */
    public function getNovelInfo(string $url): array {
        if (!$this->validateUrl($url)) {
            throw new Exception("Invalid 69书吧 URL: {$url}");
        }
        
        $this->log("Fetching novel info from: {$url}");
        
        try {
            $html = $this->makeRequest($url);
            $dom = $this->parseHtml($html);
            
            // Extract novel information
            $title = $this->extractTitle($dom);
            $author = $this->extractAuthor($dom);
            $synopsis = $this->extractSynopsis($dom);
            $publishDate = $this->extractPublishDate($dom);
            $totalChapters = $this->extractTotalChapters($dom);
            
            $this->log("Successfully extracted novel info: {$title}");
            
            return [
                'platform' => 'shuba69',
                'url' => $url,
                'original_title' => $title,
                'author' => $author,
                'original_synopsis' => $synopsis,
                'publication_date' => $publishDate,
                'total_chapters' => $totalChapters,
                'language' => 'zh'
            ];
            
        } catch (Exception $e) {
            $this->log("Error fetching novel info: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * Get chapter list from 69书吧
     */
    public function getChapterList(string $url): array {
        if (!$this->validateUrl($url)) {
            throw new Exception("Invalid 69书吧 URL: {$url}");
        }

        // Convert book info URL to chapter list URL
        $chapterListUrl = $this->getChapterListUrl($url);
        $this->log("Fetching chapter list from: {$chapterListUrl}");

        try {
            $html = $this->makeRequest($chapterListUrl);
            $dom = $this->parseHtml($html);

            $chapters = [];

            // Use the correct selector for 69shuba chapter list
            $chapterElements = $this->querySelectorAll($dom, '.catalog a');

            if ($chapterElements->length === 0) {
                // Try alternative selectors as fallback
                $selectors = [
                    '.chapter-list a',
                    '.list-chapter a',
                    '.mulu a',
                    '.book-chapter a',
                    '.chapter a',
                    '#list a',
                    '.listmain a',
                    'dd a'
                ];

                foreach ($selectors as $selector) {
                    $chapterElements = $this->querySelectorAll($dom, $selector);
                    if ($chapterElements->length > 0) {
                        $this->log("Found " . $chapterElements->length . " chapter elements using fallback selector: {$selector}");
                        break;
                    }
                }
            } else {
                $this->log("Found " . $chapterElements->length . " chapter elements using primary selector: .catalog a");
            }

            if ($chapterElements->length === 0) {
                $this->log("No chapter elements found with any selector", 'warning');
                return $chapters;
            }

            $chapterNumber = 1;
            foreach ($chapterElements as $element) {
                $chapterUrl = $element->getAttribute('href');
                $chapterTitle = $this->cleanText($element->textContent);

                // Filter out non-chapter links (navigation, book info, etc.)
                if ($this->isValidChapterLink($chapterUrl, $chapterTitle)) {
                    $fullChapterUrl = $this->normalizeUrl($chapterUrl, $this->getBaseUrlFromOriginal($chapterListUrl));

                    $chapters[] = [
                        'chapter_number' => $chapterNumber,
                        'chapter_url' => $fullChapterUrl,
                        'original_title' => $chapterTitle
                    ];

                    $chapterNumber++;
                }
            }

            // Check if chapters are in reverse order (newest first) and reverse if needed
            if (count($chapters) > 1) {
                $firstChapterTitle = $chapters[0]['original_title'];
                $lastChapterTitle = $chapters[count($chapters) - 1]['original_title'];

                // If first chapter has higher number than last, reverse the order
                if (preg_match('/第(\d+)章/', $firstChapterTitle, $firstMatch) &&
                    preg_match('/第(\d+)章/', $lastChapterTitle, $lastMatch)) {
                    if ((int)$firstMatch[1] > (int)$lastMatch[1]) {
                        $chapters = array_reverse($chapters);
                        $this->log("Reversed chapter order (was newest first)");
                    }
                }
            }

            // Re-number chapters in correct order
            foreach ($chapters as $index => &$chapter) {
                $chapter['chapter_number'] = $index + 1;
            }

            $this->log("Found " . count($chapters) . " chapters");

            return $chapters;

        } catch (Exception $e) {
            $this->log("Error fetching chapter list: " . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Convert book info URL to chapter list URL
     */
    private function getChapterListUrl(string $url): string {
        // If URL ends with .htm, remove it to get chapter list URL
        if (preg_match('/(.+)\.htm$/', $url, $matches)) {
            return $matches[1] . '/';
        }

        // If URL already ends with /, it's probably the chapter list URL
        if (substr($url, -1) === '/') {
            return $url;
        }

        // Add trailing slash
        return $url . '/';
    }

    /**
     * Validate if a link is a valid chapter link
     */
    private function isValidChapterLink(string $chapterUrl, string $chapterTitle): bool {
        if (!$chapterUrl || !$chapterTitle) {
            return false;
        }

        // Skip empty or anchor links
        if ($chapterUrl === '#' || strpos($chapterUrl, 'javascript:') === 0) {
            return false;
        }

        // Skip navigation links back to book info
        if (preg_match('/book\/\d+\.htm$/', $chapterUrl)) {
            return false;
        }

        // Must be chapter format - 69shuba uses /txt/bookid/chapterid pattern
        $validPatterns = [
            '/\/txt\/\d+\/\d+/',      // /txt/12345/67890 (69shuba format)
            '/\/\d+\/\d+\.html/',     // /12345/67890.html
            '/\/chapter\/\d+/',       // /chapter/123
            '/\/read\/\d+\/\d+/',     // /read/12345/67890
            '/\/book\/\d+\/\d+/'      // /book/12345/67890
        ];

        $isValidUrl = false;
        foreach ($validPatterns as $pattern) {
            if (preg_match($pattern, $chapterUrl)) {
                $isValidUrl = true;
                break;
            }
        }

        if (!$isValidUrl) {
            return false;
        }

        // Skip author notes and non-chapter content
        $skipPatterns = [
            '/(新书|完本感|感言|作者|序言|后记|番外|公告)/',
            '/^(目录|返回|上一页|下一页|首页|尾页)$/',
            '/^第?\s*[零一二三四五六七八九十百千万]+\s*卷$/',  // Volume titles
            '/^(从零开始缔造游戏帝国|69书吧)/',  // Site-specific navigation
            '/无弹窗.*阅读/'  // Navigation text
        ];

        foreach ($skipPatterns as $pattern) {
            if (preg_match($pattern, $chapterTitle)) {
                return false;
            }
        }

        // Must have meaningful title and look like a chapter
        $title = trim($chapterTitle);
        if (strlen($title) === 0) {
            return false;
        }

        // Should contain chapter indicators
        if (preg_match('/(第\d+章|章节|完结感言)/', $title)) {
            return true;
        }

        // If it doesn't have chapter indicators but has valid URL pattern, it might still be valid
        // but be more strict about the title length
        return strlen($title) > 3 && strlen($title) < 200;
    }
    
    /**
     * Get chapter content from 69书吧
     */
    public function getChapterContent(string $chapterUrl): array {
        $this->log("Fetching chapter content from: {$chapterUrl}");

        try {
            // Extract book ID from chapter URL for referer
            $bookId = '';
            if (preg_match('/\/txt\/(\d+)\//', $chapterUrl, $matches)) {
                $bookId = $matches[1];
            }

            // Use specific headers for 69shuba with enhanced anti-bot protection handling
            $referer = $bookId ? "https://69shuba.cx/book/{$bookId}.htm" : "https://69shuba.cx/";
            $options = [
                CURLOPT_HTTPHEADER => [
                    "Referer: {$referer}",
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7',
                    'Accept-Encoding: gzip, deflate, br',
                    'Cache-Control: no-cache',
                    'Pragma: no-cache',
                    'Sec-Fetch-Dest: document',
                    'Sec-Fetch-Mode: navigate',
                    'Sec-Fetch-Site: same-origin',
                    'Sec-Fetch-User: ?1',
                    'Upgrade-Insecure-Requests: 1',
                    'Connection: keep-alive'
                ],
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 3,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_COOKIEJAR => sys_get_temp_dir() . '/69shuba_cookies.txt',
                CURLOPT_COOKIEFILE => sys_get_temp_dir() . '/69shuba_cookies.txt'
            ];

            $html = $this->makeRequest($chapterUrl, $options);

            // Debug: Log HTML length and check if it looks like a chapter page
            $this->log("HTML response length: " . strlen($html));

            // Check if we got redirected to a listing page or blocked
            if (strpos($html, 'class="newlistbox"') !== false ||
                strpos($html, 'monthvisit_') !== false ||
                strpos($html, 'Just a moment') !== false ||
                strpos($html, 'Checking your browser') !== false) {

                $this->log("Detected redirect or blocking, attempting alternative extraction", 'warning');

                // Try alternative URL format if available
                $alternativeUrl = $this->getAlternativeChapterUrl($chapterUrl);
                if ($alternativeUrl && $alternativeUrl !== $chapterUrl) {
                    $this->log("Trying alternative URL: {$alternativeUrl}");
                    $html = $this->makeRequest($alternativeUrl, $options);
                }

                // If still blocked, throw exception
                if (strpos($html, 'class="newlistbox"') !== false ||
                    strpos($html, 'monthvisit_') !== false ||
                    strpos($html, 'Just a moment') !== false) {
                    throw new Exception("Unable to access chapter content - site protection active");
                }
            }

            $dom = $this->parseHtml($html);

            // Extract chapter title - for 69shuba it's in h1 or title
            $title = $this->extractChapterTitle($dom);

            // For 69shuba, content is directly in the page body, not in a specific container
            $content = $this->extractChapterContentDirect($dom);

            if (empty($content)) {
                throw new Exception("No content extracted from chapter");
            }

            // Enhanced content validation
            $contentLength = strlen($content);
            $wordCount = mb_strlen($content);

            if ($contentLength < 100) {
                throw new Exception("Chapter content too short: {$contentLength} characters");
            }

            // Check for content completeness indicators
            $hasBeginning = $this->validateContentBeginning($content);
            $hasEnding = $this->validateContentEnding($content);

            if (!$hasBeginning) {
                $this->log("Warning: Content may be missing beginning", 'warning');
            }

            if (!$hasEnding) {
                $this->log("Warning: Content may be missing ending", 'warning');
            }

            // Log content quality metrics
            $this->log("Content validation - Length: {$contentLength} chars, Beginning: " .
                      ($hasBeginning ? 'OK' : 'MISSING') . ", Ending: " .
                      ($hasEnding ? 'OK' : 'MISSING'));

            $this->log("Successfully extracted chapter: {$title} ({$contentLength} characters)");

            return [
                'original_title' => $title,
                'original_content' => $content,
                'word_count' => $wordCount
            ];

        } catch (Exception $e) {
            $this->log("Error fetching chapter content: " . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Validate if content has proper beginning
     */
    private function validateContentBeginning(string $content): bool {
        $firstPart = substr($content, 0, 200);

        // Check for typical story beginning patterns
        $beginningPatterns = [
            '/^["""]/',  // Starts with dialogue
            '/^[\x{4e00}-\x{9fff}]/u',  // Starts with Chinese character
            '/第\d+章/',  // Contains chapter marker
            '/^\s*[\x{4e00}-\x{9fff}].*[。！？]/u'  // Starts with text and has punctuation
        ];

        foreach ($beginningPatterns as $pattern) {
            if (preg_match($pattern, $firstPart)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate if content has proper ending
     */
    private function validateContentEnding(string $content): bool {
        $lastPart = substr($content, -200);

        // Check for typical story ending patterns
        $endingPatterns = [
            '/[。！？][""]?\s*$/',  // Ends with punctuation
            '/[。！？]\s*$/',  // Ends with Chinese punctuation
            '/完$/',  // Ends with "完" (complete)
            '/待续$/',  // Ends with "待续" (to be continued)
            '/[\x{4e00}-\x{9fff}][。！？]\s*$/u'  // Ends with character and punctuation
        ];

        foreach ($endingPatterns as $pattern) {
            if (preg_match($pattern, $lastPart)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Extract novel title
     */
    private function extractTitle(DOMDocument $dom): string {
        // First try h1 which should have the actual title
        $h1Element = $this->querySelector($dom, 'h1');
        if ($h1Element) {
            $title = $this->cleanText($h1Element->textContent);
            // Make sure it's not a generic title like "作品简介"
            if (!empty($title) && !in_array($title, ['作品简介', '简介', '目录', '章节列表'])) {
                $this->log("Title extracted using h1: {$title}");
                return $title;
            }
        }

        // Try other selectors
        $titleSelectors = [
            '.book-title',
            '.bookname',
            '.book-info h1',
            '.info h1',
            '.book h1',
            '#info h1'
        ];

        foreach ($titleSelectors as $selector) {
            $titleElement = $this->querySelector($dom, $selector);
            if ($titleElement) {
                $title = $this->cleanText($titleElement->textContent);
                if (!empty($title) && !in_array($title, ['作品简介', '简介', '目录', '章节列表'])) {
                    $this->log("Title extracted using selector '{$selector}': {$title}");
                    return $title;
                }
            }
        }

        // Try to extract from page title
        $titleElement = $this->querySelector($dom, 'title');
        if ($titleElement) {
            $pageTitle = $this->cleanText($titleElement->textContent);
            // Remove common suffixes from page title
            $title = preg_replace('/[-_|].*?(69书吧|69shuba|小说|阅读|txt|下载).*$/i', '', $pageTitle);
            $title = trim($title);
            if (!empty($title)) {
                $this->log("Title extracted from page title: {$title}");
                return $title;
            }
        }

        $this->log("Title extraction failed - no matching elements found", 'warning');
        return '';
    }

    /**
     * Extract author name
     */
    private function extractAuthor(DOMDocument $dom): string {
        // Try to find author in paragraph elements (most reliable for this site)
        $paragraphs = $this->querySelectorAll($dom, 'p');
        foreach ($paragraphs as $p) {
            $text = $this->cleanText($p->textContent);

            // Check if this paragraph contains author info
            if (strpos($text, '作者') !== false) {
                // Pattern: 作者：AuthorName (capture everything after 作者：)
                if (preg_match('/作者[：:]\s*(.+)$/', $text, $matches)) {
                    $author = trim($matches[1]);
                    // Make sure it's not part of a longer navigation text
                    if (!empty($author) && strlen($author) < 50 && !preg_match('/(小说|阅读|下载|69书吧|首页|排行|分类)/', $author)) {
                        $this->log("Author extracted from paragraph: {$author}");
                        return $author;
                    }
                }
            }
        }

        // Try to find author in text content
        $xpath = new DOMXPath($dom);
        $textNodes = $xpath->query('//text()[contains(., "作者")]');
        foreach ($textNodes as $textNode) {
            $text = $textNode->textContent;

            // Pattern: 作者：AuthorName
            if (preg_match('/作者[：:]\s*([^分类\n\r]+?)(?:\s|$)/', $text, $matches)) {
                $author = trim($matches[1]);
                // Make sure it's not part of a longer navigation text
                if (!empty($author) && strlen($author) < 50 && !preg_match('/(小说|阅读|下载|69书吧)/', $author)) {
                    $this->log("Author extracted from text content: {$author}");
                    return $author;
                }
            }
        }

        // Try structured selectors
        $authorSelectors = [
            '.book-author',
            '.author',
            '.author-name',
            '.writer',
            '.writer-name',
            '.book-info .author',
            '.meta .author',
            '.info .author',
            '#info .author',
            '.book .author',
            '.bookinfo .author'
        ];

        foreach ($authorSelectors as $selector) {
            $authorElement = $this->querySelector($dom, $selector);
            if ($authorElement) {
                $authorText = $this->cleanText($authorElement->textContent);

                // Remove common prefixes and clean up
                $authorText = preg_replace('/^(作者[：:]?\s*|著者[：:]?\s*|Author[：:]?\s*)/u', '', $authorText);
                $authorText = preg_replace('/\s*(作者|著者|Author)\s*$/', '', $authorText);
                $authorText = trim($authorText);

                // Make sure it's not navigation text
                if (!empty($authorText) && strlen($authorText) > 1 && strlen($authorText) < 50 &&
                    !preg_match('/(小说|阅读|下载|69书吧|首页|排行|分类)/', $authorText)) {
                    $this->log("Author extracted using selector '{$selector}': {$authorText}");
                    return $authorText;
                }
            }
        }

        // Try to extract from page structure - look for spans or divs near title
        $h1Element = $this->querySelector($dom, 'h1');
        if ($h1Element && $h1Element->parentNode) {
            $parent = $h1Element->parentNode;
            $siblings = $parent->childNodes;

            foreach ($siblings as $sibling) {
                if ($sibling->nodeType === XML_ELEMENT_NODE) {
                    $text = $this->cleanText($sibling->textContent);
                    if (preg_match('/作者[：:]\s*([^分类\n\r]+?)(?:\s|$)/', $text, $matches)) {
                        $author = trim($matches[1]);
                        if (!empty($author) && strlen($author) < 50) {
                            $this->log("Author extracted from sibling element: {$author}");
                            return $author;
                        }
                    }
                }
            }
        }

        $this->log("Author extraction failed - no matching elements found", 'warning');
        return '';
    }

    /**
     * Extract synopsis
     */
    private function extractSynopsis(DOMDocument $dom): string {
        $synopsisSelectors = [
            '.book-summary',
            '.summary',
            '.intro',
            '.description',
            '.book-intro',
            '.book-description',
            '.content-summary',
            '.synopsis',
            '.book-info .intro',
            '.info .intro',
            '#info .intro',
            '.bookinfo .intro',
            '.book .intro',
            '.jieshao'
        ];

        foreach ($synopsisSelectors as $selector) {
            $synopsisElement = $this->querySelector($dom, $selector);
            if ($synopsisElement) {
                $synopsisText = $this->cleanText($synopsisElement->textContent);

                // Remove common prefixes
                $synopsisText = preg_replace('/^(内容简介[：:]?\s*|简介[：:]?\s*|介绍[：:]?\s*)/u', '', $synopsisText);
                $synopsisText = trim($synopsisText);

                if (!empty($synopsisText) && strlen($synopsisText) > 10) {
                    $this->log("Synopsis extracted using selector '{$selector}': " . substr($synopsisText, 0, 100) . "...");
                    return $synopsisText;
                }
            }
        }

        // Try to find synopsis in paragraphs
        $paragraphs = $this->querySelectorAll($dom, 'p');
        foreach ($paragraphs as $p) {
            $text = $this->cleanText($p->textContent);
            if (strlen($text) > 50 &&
                (strpos($text, '简介') !== false ||
                 strpos($text, '内容') !== false ||
                 strpos($text, '介绍') !== false)) {

                $text = preg_replace('/^(内容简介[：:]?\s*|简介[：:]?\s*|介绍[：:]?\s*)/u', '', $text);
                $text = trim($text);

                if (strlen($text) > 20) {
                    $this->log("Synopsis extracted from paragraph: " . substr($text, 0, 100) . "...");
                    return $text;
                }
            }
        }

        $this->log("Synopsis extraction failed - no matching elements found", 'warning');
        return '';
    }
    
    /**
     * Extract publication date
     */
    private function extractPublishDate(DOMDocument $dom): ?string {
        // Try to find date in paragraph elements (most reliable for this site)
        $paragraphs = $this->querySelectorAll($dom, 'p');
        foreach ($paragraphs as $p) {
            $text = $this->cleanText($p->textContent);

            // Look for "更新：" pattern
            if (preg_match('/更新[：:]\s*(\d{4}-\d{1,2}-\d{1,2})/', $text, $matches)) {
                $date = $this->extractDate($matches[1]);
                if ($date) {
                    $this->log("Publication date extracted from paragraph: {$date}");
                    return $date;
                }
            }
        }

        $dateSelectors = [
            '.book-meta',
            '.book-info',
            '.meta',
            '.publish-date',
            '.created-date',
            '.date',
            '.book-date',
            '.info',
            '#info',
            '.bookinfo',
            '.book .info'
        ];

        foreach ($dateSelectors as $selector) {
            $dateElements = $this->querySelectorAll($dom, $selector);

            foreach ($dateElements as $element) {
                $dateText = $this->cleanText($element->textContent);

                // Look for Chinese date keywords
                if (strpos($dateText, '更新时间') !== false ||
                    strpos($dateText, '更新') !== false ||
                    strpos($dateText, '发布时间') !== false ||
                    strpos($dateText, '上传时间') !== false ||
                    strpos($dateText, '创建时间') !== false ||
                    strpos($dateText, '时间') !== false ||
                    preg_match('/\d{4}/', $dateText)) {

                    $date = $this->extractDate($dateText);
                    if ($date) {
                        $this->log("Publication date extracted using selector '{$selector}': {$date}");
                        return $date;
                    }
                }
            }
        }

        // Try to find date in text content with specific patterns
        $xpath = new DOMXPath($dom);
        $textNodes = $xpath->query('//text()[contains(., "更新") or contains(., "时间") or contains(., "日期") or contains(., "年") or contains(., "月")]');

        foreach ($textNodes as $textNode) {
            $text = $textNode->textContent;

            // Look for date patterns with Chinese keywords
            $patterns = [
                '/(?:更新|更新时间|发布时间|上传时间|创建时间)[：:]\s*(\d{4}[-\/年]\d{1,2}[-\/月]\d{1,2}[日]?)/',
                '/(\d{4}[-\/年]\d{1,2}[-\/月]\d{1,2}[日]?)/',
                '/(\d{4}年\d{1,2}月\d{1,2}日)/',
                '/(\d{4}-\d{1,2}-\d{1,2})/',
                '/(\d{4}\/\d{1,2}\/\d{1,2})/'
            ];

            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $text, $matches)) {
                    $date = $this->extractDate($matches[1]);
                    if ($date) {
                        $this->log("Publication date extracted from text content: {$date}");
                        return $date;
                    }
                }
            }
        }

        // Try to find date in meta tags
        $metaElements = $this->querySelectorAll($dom, 'meta[property*="date"], meta[name*="date"], meta[property*="time"], meta[name*="time"]');
        foreach ($metaElements as $element) {
            $content = $element->getAttribute('content');
            if ($content) {
                $date = $this->extractDate($content);
                if ($date) {
                    $this->log("Publication date extracted from meta tag: {$date}");
                    return $date;
                }
            }
        }

        // Try to extract from first chapter if available
        $firstChapterDate = $this->extractFirstChapterDate($dom);
        if ($firstChapterDate) {
            $this->log("Publication date extracted from first chapter: {$firstChapterDate}");
            return $firstChapterDate;
        }

        $this->log("Publication date extraction failed - no matching elements found", 'warning');
        return null;
    }

    /**
     * Extract publication date from the first chapter
     */
    private function extractFirstChapterDate(DOMDocument $dom): ?string {
        // Look for the first chapter link and try to extract date from its context
        $chapterSelectors = [
            '.catalog a:first-child',
            '.chapter-list a:first-child',
            '.list-chapter a:first-child',
            '.mulu a:first-child',
            '#list a:first-child'
        ];

        foreach ($chapterSelectors as $selector) {
            $firstChapter = $this->querySelector($dom, $selector);
            if ($firstChapter) {
                // Look for date in the parent element or siblings
                $parent = $firstChapter->parentNode;
                if ($parent) {
                    $parentText = $this->cleanText($parent->textContent);
                    $date = $this->extractDate($parentText);
                    if ($date) {
                        return $date;
                    }
                }

                // Check next sibling for date
                $nextSibling = $firstChapter->nextSibling;
                while ($nextSibling) {
                    if ($nextSibling->nodeType === XML_TEXT_NODE || $nextSibling->nodeType === XML_ELEMENT_NODE) {
                        $siblingText = $this->cleanText($nextSibling->textContent);
                        $date = $this->extractDate($siblingText);
                        if ($date) {
                            return $date;
                        }
                    }
                    $nextSibling = $nextSibling->nextSibling;

                    // Only check a few siblings
                    if (!$nextSibling || $nextSibling->nodeType === XML_ELEMENT_NODE) {
                        break;
                    }
                }
            }
        }

        return null;
    }
    
    /**
     * Extract total chapters
     */
    private function extractTotalChapters(DOMDocument $dom): int {
        // Count chapters in the catalog
        $chapterElements = $this->querySelectorAll($dom, '.catalog a');
        if ($chapterElements->length > 0) {
            return $chapterElements->length;
        }
        
        // Try alternative selectors
        $chapterElements = $this->querySelectorAll($dom, '.chapter-list a');
        if ($chapterElements->length > 0) {
            return $chapterElements->length;
        }
        
        // Look for chapter count in text
        $allText = $dom->textContent;
        if (preg_match('/共(\d+)章/', $allText, $matches)) {
            return (int) $matches[1];
        }
        
        if (preg_match('/(\d+)章/', $allText, $matches)) {
            return (int) $matches[1];
        }
        
        return 0;
    }
    
    /**
     * Extract chapter title with fallback selectors
     */
    private function extractChapterTitle(DOMDocument $dom): string {
        // For 69shuba, try h1 first
        $h1Element = $this->querySelector($dom, 'h1');
        if ($h1Element) {
            $title = $this->cleanText($h1Element->textContent);
            if (!empty($title) && !in_array($title, ['目录', '章节列表', '首页'])) {
                return $title;
            }
        }

        // Try to extract from page title
        $titleElement = $this->querySelector($dom, 'title');
        if ($titleElement) {
            $pageTitle = $this->cleanText($titleElement->textContent);
            // Extract chapter title from page title (format: "小说名-第X章 章节名")
            if (preg_match('/^(.+?)-(.+?)$/', $pageTitle, $matches)) {
                $chapterTitle = trim($matches[2]);
                // Remove site name suffix
                $chapterTitle = preg_replace('/[-_|].*?(69书吧|69shuba).*$/i', '', $chapterTitle);
                $chapterTitle = trim($chapterTitle);
                if (!empty($chapterTitle)) {
                    return $chapterTitle;
                }
            }
        }

        // Try other selectors as fallback
        $titleSelectors = [
            '.chapter-title',
            '.book-title',
            '.title',
            '.content-title'
        ];

        foreach ($titleSelectors as $selector) {
            $titleElement = $this->querySelector($dom, $selector);
            if ($titleElement) {
                $title = $this->cleanText($titleElement->textContent);
                if (!empty($title)) {
                    return $title;
                }
            }
        }

        return '';
    }

    /**
     * Find content element with fallback selectors
     */
    private function findContentElement(DOMDocument $dom): ?DOMElement {
        $contentSelectors = [
            '.chapter-content',
            '.content',
            '#content',
            '.book-content',
            '.text-content',
            '.novel-content',
            '.txt',
            '.main-text',
            '.chapter-text',
            '.read-content',
            '.article-content',
            '.story-content',
            '#main',
            '.main',
            '.readcontent',
            '.bookcontent'
        ];

        foreach ($contentSelectors as $selector) {
            $contentElement = $this->querySelector($dom, $selector);
            if ($contentElement) {
                // Check if element has meaningful content
                $text = trim($contentElement->textContent);
                if (!empty($text) && strlen($text) > 50) {
                    $this->log("Found content element using selector: {$selector}");
                    return $contentElement;
                }
            }
        }

        // If no specific content element found, try to find the main content area
        // Look for divs with substantial text content
        $xpath = new DOMXPath($dom);
        $divs = $xpath->query('//div[string-length(normalize-space(text())) > 100]');

        foreach ($divs as $div) {
            $text = trim($div->textContent);
            // Skip navigation and header content
            if (strlen($text) > 200 &&
                !preg_match('/(排行|导航|菜单|登录|注册|搜索|首页)/', $text) &&
                !preg_match('/class="(menu|nav|header|footer|sidebar)"/', $div->getAttribute('class'))) {
                $this->log("Found content element using text length heuristic");
                return $div;
            }
        }

        return null;
    }

    /**
     * Extract chapter content directly from 69shuba page structure
     */
    protected function extractChapterContentDirect(DOMDocument $dom): string {
        $content = '';

        // FIXED: Try the new comprehensive strategy first
        $content = $this->extractContentStrategyComprehensive($dom);

        if (empty($content) || strlen($content) < 50) {
            $this->log("Comprehensive strategy failed, trying 69Shuba strategy", 'warning');
            $content = $this->extractContentStrategy69Shuba($dom);
        }

        if (empty($content) || strlen($content) < 50) { // FIXED: Reduced threshold from 200 to 50
            $this->log("69Shuba strategy failed, trying strategy 1", 'warning');
            $content = $this->extractContentStrategy1($dom);
        }

        if (empty($content) || strlen($content) < 50) { // FIXED: Reduced threshold from 200 to 50
            $this->log("Strategy 1 failed or insufficient content, trying strategy 2", 'warning');
            $content = $this->extractContentStrategy2($dom);
        }

        if (empty($content) || strlen($content) < 50) { // FIXED: Reduced threshold from 200 to 50
            $this->log("Strategy 2 failed or insufficient content, trying strategy 3", 'warning');
            $content = $this->extractContentStrategy3($dom);
        }

        // Clean up the content
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        $content = trim($content);

        // FIXED: More lenient final validation
        if (strlen($content) < 20) { // FIXED: Reduced from 100 to 20
            $this->log("Warning: Extracted content is very short (" . strlen($content) . " characters)", 'warning');
        } else {
            $this->log("Content extraction successful: " . strlen($content) . " characters");
        }

        return $content;
    }

    /**
     * NEW: Comprehensive strategy that tries multiple approaches to capture all content
     */
    private function extractContentStrategyComprehensive(DOMDocument $dom): string {
        $xpath = new DOMXPath($dom);
        $allContent = [];

        // Method 0: Try the date-based extraction first for 69shuba
        $dateContent = $this->extractContentAfterDate($dom);
        if (!empty($dateContent) && strlen($dateContent) > 50) {
            $this->log("Comprehensive strategy: date-based extraction successful");
            return $dateContent;
        }

        // Method 1: Try to find the main content container
        $contentContainers = [
            '//div[contains(@class, "mybox")]',
            '//div[contains(@class, "content")]',
            '//div[contains(@class, "chapter")]',
            '//div[contains(@class, "text")]',
            '//main',
            '//article'
        ];

        foreach ($contentContainers as $selector) {
            $containers = $xpath->query($selector);
            if ($containers->length > 0) {
                foreach ($containers as $container) {
                    // Extract all text content, preserving structure
                    $content = $this->extractAllTextFromElement($container);
                    if (strlen($content) > 50) {
                        $allContent[] = $content;
                    }
                }
            }
        }

        // Method 2: If no containers found, extract from body but filter navigation
        if (empty($allContent)) {
            $body = $xpath->query('//body')->item(0);
            if ($body) {
                $content = $this->extractAllTextFromElement($body);
                if (strlen($content) > 50) {
                    $allContent[] = $content;
                }
            }
        }

        // Combine and clean all content
        $finalContent = implode("\n\n", $allContent);

        if (!empty($finalContent)) {
            $this->log("Comprehensive strategy successful: " . strlen($finalContent) . " characters");
            return $this->cleanExtractedContent($finalContent);
        }

        $this->log("Comprehensive strategy failed to extract content", 'warning');
        return '';
    }

    /**
     * Extract all text from an element while preserving structure and filtering navigation
     */
    private function extractAllTextFromElement(DOMElement $element): string {
        $xpath = new DOMXPath($element->ownerDocument);
        $textParts = [];

        // Method 1: Try to extract from paragraphs first
        $paragraphs = $xpath->query('.//p', $element);
        if ($paragraphs->length > 0) {
            foreach ($paragraphs as $p) {
                $text = trim($p->textContent);
                if ($this->isValidContentParagraph($text)) {
                    $textParts[] = $text;
                }
            }
        }

        // Method 2: If no paragraphs, extract from divs
        if (empty($textParts)) {
            $divs = $xpath->query('.//div', $element);
            foreach ($divs as $div) {
                $text = trim($div->textContent);
                if ($this->isValidContentParagraph($text)) {
                    // Check if this div contains other divs - if so, skip to avoid duplication
                    $childDivs = $xpath->query('.//div', $div);
                    if ($childDivs->length === 0) {
                        $textParts[] = $text;
                    }
                }
            }
        }

        // Method 3: If still no content, extract all text nodes
        if (empty($textParts)) {
            $textNodes = $xpath->query('.//text()[normalize-space(.) != ""]', $element);
            $currentParagraph = '';

            foreach ($textNodes as $textNode) {
                $text = trim($textNode->textContent);
                if (!empty($text)) {
                    $currentParagraph .= $text . ' ';

                    // Check if this text node is followed by a block element
                    $parent = $textNode->parentNode;
                    if ($parent && in_array(strtolower($parent->nodeName), ['p', 'div', 'br'])) {
                        if (!empty($currentParagraph)) {
                            $cleanParagraph = trim($currentParagraph);
                            if ($this->isValidContentParagraph($cleanParagraph)) {
                                $textParts[] = $cleanParagraph;
                            }
                            $currentParagraph = '';
                        }
                    }
                }
            }

            // Add any remaining paragraph
            if (!empty($currentParagraph)) {
                $cleanParagraph = trim($currentParagraph);
                if ($this->isValidContentParagraph($cleanParagraph)) {
                    $textParts[] = $cleanParagraph;
                }
            }
        }

        return implode("\n\n", $textParts);
    }

    /**
     * Check if a paragraph contains valid content (not navigation)
     */
    private function isValidContentParagraph(string $text): bool {
        // Skip empty or very short text
        if (empty($text) || strlen($text) < 3) {
            return false;
        }

        // Skip obvious navigation patterns (exact matches only)
        $navigationPatterns = [
            '/^(目录|上一章|下一章|首页|书签|设置|夜间|返回|收藏本站|Copyright|69书吧|TXT下载|全文阅读)$/u',
            '/^第\d+章\s*$/u', // Chapter title only
            '/^(点击|阅读|下载|收藏|推荐|投票|评论).*$/u'
        ];

        foreach ($navigationPatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Strategy 69Shuba: Optimized extraction for 69shuba.cx structure
     */
    private function extractContentStrategy69Shuba(DOMDocument $dom): string {
        $xpath = new DOMXPath($dom);
        $content = '';

        // NEW Method: Extract content after date element following the specific pattern
        $content = $this->extractContentAfterDate($dom);
        if (!empty($content) && strlen($content) > 50) {
            $this->log("69Shuba date-based extraction successful: " . strlen($content) . " characters");
            return $this->formatExtractedText($content);
        }

        // Method 1: Extract content between txtnav and page1 divs
        $myboxElements = $xpath->query('//div[contains(@class, "mybox")]');

        if ($myboxElements->length > 0) {
            $mybox = $myboxElements->item(0);
            $innerHTML = $this->getInnerHTML($mybox);

            // Find content between txtnav and page1
            $pattern = '/(<div[^>]*class="[^"]*txtnav[^"]*"[^>]*>.*?<\/div>)(.*?)(<div[^>]*class="[^"]*page1[^"]*"[^>]*>)/s';

            if (preg_match($pattern, $innerHTML, $matches)) {
                $contentHtml = $matches[2];

                // Parse the content HTML
                $contentDom = new DOMDocument();
                $contentDom->loadHTML('<?xml encoding="UTF-8">' . $contentHtml, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

                $content = $this->extractTextFromContentHtml($contentDom);

                if (!empty($content) && strlen($content) > 20) { // FIXED: Reduced from 100 to 20
                    $this->log("69Shuba method 1 successful: " . strlen($content) . " characters");
                    return $this->formatExtractedText($content);
                }
            }
        }

        // Method 2: Direct paragraph extraction from mybox
        $paragraphs = $xpath->query('//div[contains(@class, "mybox")]//p');
        $contentParts = [];

        foreach ($paragraphs as $p) {
            $text = $this->cleanText($p->textContent);
            if (!empty($text) && strlen($text) > 2) { // FIXED: Reduced from 5 to 2
                // FIXED: More conservative navigation filtering - only skip exact matches
                if (!preg_match('/^(上一章|下一章|目录|书签|收藏|设置|报错|Copyright|69书吧)$/', $text)) {
                    $contentParts[] = $text;
                }
            }
        }

        // Also get direct text nodes after txtnav
        $txtnavElements = $xpath->query('//div[contains(@class, "txtnav")]');
        if ($txtnavElements->length > 0) {
            $txtnavParent = $txtnavElements->item(0)->parentNode;
            $foundTxtnav = false;

            foreach ($txtnavParent->childNodes as $node) {
                if ($foundTxtnav) {
                    if ($node->nodeType === XML_ELEMENT_NODE &&
                        strpos($node->getAttribute('class'), 'page1') !== false) {
                        break; // Stop at page1
                    }

                    if ($node->nodeType === XML_TEXT_NODE) {
                        $text = trim($node->textContent);
                        if (!empty($text) && strlen($text) > 3) { // FIXED: Reduced from 10 to 3
                            $contentParts[] = $text;
                        }
                    }
                }

                if ($node->nodeType === XML_ELEMENT_NODE &&
                    strpos($node->getAttribute('class'), 'txtnav') !== false) {
                    $foundTxtnav = true;
                }
            }
        }

        if (!empty($contentParts)) {
            $content = implode("\n\n", $contentParts);
            $this->log("69Shuba method 2 successful: " . strlen($content) . " characters");
            return $this->formatExtractedText($content);
        }

        $this->log("69Shuba strategy failed to extract content", 'warning');
        return '';
    }

    /**
     * Extract text content from HTML content with proper Chinese character handling
     */
    private function extractTextFromContentHtml(DOMDocument $dom): string {
        $xpath = new DOMXPath($dom);
        $contentParts = [];

        // Get all text nodes and paragraph elements
        $textNodes = $xpath->query('//text()[normalize-space(.) != ""]');
        $paragraphs = $xpath->query('//p');

        // Process paragraphs first with Chinese character-aware filtering
        foreach ($paragraphs as $p) {
            $text = $this->cleanText($p->textContent);

            // Filter out JavaScript code like "loadAdv(2, 0);"
            if (preg_match('/^loadAdv\s*\(\s*\d+\s*,\s*\d+\s*\)\s*;?\s*$/', $text)) {
                continue;
            }

            // Use mb_strlen for proper Chinese character counting
            if (!empty($text) && mb_strlen($text, 'UTF-8') > 1) {
                $contentParts[] = $text;
            }
        }

        // FIXED: If no paragraphs, process text nodes with more lenient filtering
        if (empty($contentParts)) {
            foreach ($textNodes as $textNode) {
                $text = trim($textNode->textContent);

                // Filter out JavaScript code like "loadAdv(2, 0);"
                if (preg_match('/^loadAdv\s*\(\s*\d+\s*,\s*\d+\s*\)\s*;?\s*$/', $text)) {
                    continue;
                }

                if (!empty($text) && strlen($text) > 3) { // FIXED: Reduced from 10 to 3
                    $contentParts[] = $text;
                }
            }
        }

        // Join paragraphs with double newlines to preserve paragraph structure
        return implode("\n\n", $contentParts);
    }

    /**
     * Extract content after date element following 69shuba pattern:
     * Title -> Date -> Duplicate Title -> Content -> (本章完)
     */
    protected function extractContentAfterDate(DOMDocument $dom): string {
        $xpath = new DOMXPath($dom);
        $allText = $dom->textContent;

        // Look for the specific 69shuba pattern: Date -> Duplicate Title -> Content -> (本章完)
        // We need to find the date, then skip the duplicate title, then capture the actual content
        $datePatterns = [
            // Pattern: Date followed by duplicate chapter title, then content until end marker
            '/(\d{4}-\d{1,2}-\d{1,2})\s*第\d+章[^\n]*\n(.+?)(\(本章完\))?/s',
            '/(\d{4}年\d{1,2}月\d{1,2}日)\s*第\d+章[^\n]*\n(.+?)(\(本章完\))?/s',
            '/(\d{4}\/\d{1,2}\/\d{1,2})\s*第\d+章[^\n]*\n(.+?)(\(本章完\))?/s',

            // Fallback patterns without explicit chapter title
            '/(\d{4}-\d{1,2}-\d{1,2})\s*([^第].+?)(\(本章完\))?/s',
            '/(\d{4}年\d{1,2}月\d{1,2}日)\s*([^第].+?)(\(本章完\))?/s',
            '/(\d{4}\/\d{1,2}\/\d{1,2})\s*([^第].+?)(\(本章完\))?/s'
        ];

        foreach ($datePatterns as $pattern) {
            if (preg_match($pattern, $allText, $matches)) {
                $this->log("Date pattern matched: " . substr($pattern, 0, 50) . "...");

                $contentAfterDate = trim($matches[2]);

                // Add the chapter end marker if it was found
                if (isset($matches[3]) && !empty($matches[3])) {
                    $contentAfterDate .= $matches[3];
                }

                $this->log("Raw content after date (before cleaning): " . strlen($contentAfterDate) . " characters");

                // Clean up the content - remove navigation and duplicate titles
                $contentAfterDate = $this->cleanContentAfterDate($contentAfterDate);

                if (strlen($contentAfterDate) > 50) {
                    $this->log("Found content after date pattern: " . strlen($contentAfterDate) . " characters");
                    return $contentAfterDate;
                }
            }
        }

        // Alternative approach: Find date elements in DOM and extract content after them
        $this->log("Trying DOM-based date extraction...");
        $dateElements = $xpath->query('//text()[contains(., "-") and string-length(.) < 20]');

        foreach ($dateElements as $dateNode) {
            $dateText = trim($dateNode->textContent);

            // Check if this looks like a date
            if (preg_match('/^\d{4}[-\/年]\d{1,2}[-\/月]\d{1,2}[日]?$/', $dateText)) {
                $this->log("Found date element: " . $dateText);

                // Find the parent element and get all following content
                $parent = $dateNode->parentNode;
                $content = $this->extractContentAfterElementImproved($parent, $dateNode);

                if (strlen($content) > 50) {
                    $this->log("Found content after date element: " . strlen($content) . " characters");
                    return $this->cleanContentAfterDate($content);
                }
            }
        }

        // Final fallback: Try to extract from the main content area
        $this->log("Trying fallback content extraction...");
        $contentAreas = $xpath->query('//div[contains(@class, "mybox")]//text()[string-length(.) > 20]');
        if ($contentAreas->length > 0) {
            $allContent = '';
            foreach ($contentAreas as $textNode) {
                $text = trim($textNode->textContent);
                if (!empty($text) && !preg_match('/^(目录|上一章|下一章|首页|书签|设置|夜间|返回|收藏本站|Copyright|69书吧)$/', $text)) {
                    $allContent .= $text . "\n\n";
                }
            }

            if (strlen($allContent) > 100) {
                $this->log("Fallback extraction successful: " . strlen($allContent) . " characters");
                return $this->cleanContentAfterDate($allContent);
            }
        }

        return '';
    }

    /**
     * Extract content that appears after a specific element
     */
    protected function extractContentAfterElement(DOMElement $element): string {
        $content = '';
        $foundElement = false;
        $parent = $element->parentNode;

        if (!$parent) {
            return '';
        }

        // Walk through all nodes after the date element
        foreach ($parent->childNodes as $node) {
            if ($foundElement) {
                if ($node->nodeType === XML_TEXT_NODE) {
                    $text = trim($node->textContent);
                    if (!empty($text)) {
                        $content .= $text . "\n";
                    }
                } elseif ($node->nodeType === XML_ELEMENT_NODE) {
                    // Stop if we hit navigation elements
                    if (strpos($node->getAttribute('class'), 'page1') !== false ||
                        strpos($node->getAttribute('class'), 'txtnav') !== false) {
                        break;
                    }

                    $text = trim($node->textContent);
                    if (!empty($text)) {
                        $content .= $text . "\n";
                    }
                }
            }

            if ($node === $element) {
                $foundElement = true;
            }
        }

        return $content;
    }

    /**
     * Improved content extraction after a specific date element
     */
    protected function extractContentAfterElementImproved(DOMElement $parentElement, DOMNode $dateNode): string {
        $content = '';
        $foundDate = false;
        $skippedDuplicateTitle = false;

        // Get all text nodes in the parent element
        $xpath = new DOMXPath($parentElement->ownerDocument);
        $allNodes = $xpath->query('.//text()[normalize-space(.) != ""]', $parentElement);

        foreach ($allNodes as $node) {
            if ($foundDate) {
                $text = trim($node->textContent);

                if (empty($text)) {
                    continue;
                }

                // Skip the duplicate chapter title that comes right after the date
                if (!$skippedDuplicateTitle && preg_match('/^第\d+章/', $text)) {
                    $skippedDuplicateTitle = true;
                    $this->log("Skipped duplicate chapter title: " . substr($text, 0, 50));
                    continue;
                }

                // Stop if we hit navigation elements
                if (preg_match('/^(目录|上一章|下一章|首页|书签|设置|夜间|返回|收藏本站|Copyright|69书吧)$/', $text)) {
                    break;
                }

                // Stop if we hit the chapter end marker (but include it)
                if (strpos($text, '(本章完)') !== false) {
                    $content .= $text . "\n\n";
                    break;
                }

                $content .= $text . "\n\n";
            }

            // Check if this is our date node
            if ($node === $dateNode) {
                $foundDate = true;
                $this->log("Found date node, starting content extraction");
            }
        }

        return $content;
    }

    /**
     * Clean content extracted after date element with proper Chinese character handling
     */
    protected function cleanContentAfterDate(string $content): string {
        $this->log("cleanContentAfterDate input length: " . mb_strlen($content, 'UTF-8') . " characters");

        // Ensure proper UTF-8 encoding
        if (!mb_check_encoding($content, 'UTF-8')) {
            $encoding = mb_detect_encoding($content, ['UTF-8', 'GB2312', 'GBK', 'BIG5'], true);
            if ($encoding) {
                $content = mb_convert_encoding($content, 'UTF-8', $encoding);
                $this->log("Converted encoding from $encoding to UTF-8");
            }
        }

        // Remove JavaScript code like "loadAdv(2, 0);" at the beginning
        $content = preg_replace('/^loadAdv\s*\(\s*\d+\s*,\s*\d+\s*\)\s*;?\s*\n?/', '', $content);

        // VERY CONSERVATIVE: Only remove duplicate chapter titles if they're clearly at the very beginning
        // and followed by actual content (not navigation)
        if (preg_match('/^第\d+章[^\n]*\n(.{20,})/s', $content, $matches)) {
            // Only remove if there's substantial content after the title
            $content = $matches[1];
            $this->log("Removed duplicate chapter title, remaining: " . mb_strlen($content, 'UTF-8') . " characters");
        }

        // Remove navigation elements only if they appear as standalone lines
        $content = preg_replace('/^(目录|上一章|下一章|首页|书签|设置|夜间|返回|收藏本站)$\n?/m', '', $content);

        // PRESERVE the chapter end marker "(本章完)" instead of removing it
        // Only remove content AFTER the marker, not the marker itself
        if (preg_match('/^(.+?)\(本章完\)(.*)$/s', $content, $matches)) {
            $content = $matches[1] . '(本章完)';
            $this->log("Preserved chapter end marker, content length: " . mb_strlen($content, 'UTF-8') . " characters");
        }

        // Remove 69shuba specific watermarks
        $content = preg_replace('/最⊥新⊥小⊥说⊥在⊥六⊥9⊥⊥书⊥⊥吧⊥⊥首⊥发！/', '', $content);

        // Clean up whitespace but preserve Chinese text structure
        // Ensure proper paragraph breaks by adding breaks after sentence endings
        $content = preg_replace('/([。！？])(?!\s*\n)/', '$1' . "\n\n", $content);
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        $content = trim($content);

        $this->log("cleanContentAfterDate output length: " . mb_strlen($content, 'UTF-8') . " characters");
        return $content;
    }

    /**
     * Get innerHTML of an element
     */
    private function getInnerHTML(DOMElement $element): string {
        $innerHTML = '';
        foreach ($element->childNodes as $child) {
            $innerHTML .= $element->ownerDocument->saveHTML($child);
        }
        return $innerHTML;
    }

    /**
     * Clean extracted content - FIXED: More conservative cleaning to prevent content loss
     */
    private function cleanExtractedContent(string $content): string {
        // Remove JavaScript code like "loadAdv(2, 0);" at the beginning
        $content = preg_replace('/^loadAdv\s*\(\s*\d+\s*,\s*\d+\s*\)\s*;?\s*\n?/', '', $content);

        // For date-based extraction, be more conservative about removing chapter titles
        // Only remove if it's clearly at the very beginning and followed by navigation
        if (preg_match('/^第\d+章[^\n]*\n(目录|上一章|下一章)/', $content)) {
            $content = preg_replace('/^第\d+章[^\n]*\n?/', '', $content);
        }

        // FIXED: Only remove navigation text if it appears at the end of a line (more conservative)
        $content = preg_replace('/\n(上一章|下一章|目录|书签|收藏|设置|报错|手机上看)[^\n]*$/m', '', $content);

        // Remove specific 69shuba watermarks
        $content = preg_replace('/最⊥新⊥小⊥说⊥在⊥六⊥9⊥⊥书⊥⊥吧⊥⊥首⊥发！/', '', $content);

        // PRESERVE the chapter end marker "(本章完)" instead of removing it
        // Only remove content AFTER the marker, not the marker itself
        if (preg_match('/^(.+?)\(本章完\)(.*)$/s', $content, $matches)) {
            $content = $matches[1] . '(本章完)';
        }

        // FIXED: Only remove copyright/site info if it's at the very end
        $content = preg_replace('/\n?(Copyright.*|69书吧.*)$/s', '', $content);

        // FIXED: More conservative whitespace cleanup - preserve paragraph structure
        // Ensure proper paragraph breaks by adding breaks after sentence endings
        $content = preg_replace('/([。！？])(?!\s*\n)/', '$1' . "\n\n", $content);
        $content = preg_replace('/\n{3,}/', "\n\n", $content); // Max 2 newlines for paragraph breaks
        $content = preg_replace('/[ \t]+/', ' ', $content); // Clean up spaces/tabs
        $content = trim($content);

        return $content;
    }

    /**
     * Strategy 1: Conservative paragraph-based extraction - FIXED: More inclusive content extraction
     */
    private function extractContentStrategy1(DOMDocument $dom): string {
        $xpath = new DOMXPath($dom);
        $paragraphs = $xpath->query('//p');
        $contentParagraphs = [];

        foreach ($paragraphs as $paragraph) {
            $text = $this->cleanText($paragraph->textContent);

            // Skip empty paragraphs
            if (empty($text)) {
                continue;
            }

            // Filter out JavaScript code like "loadAdv(2, 0);"
            if (preg_match('/^loadAdv\s*\(\s*\d+\s*,\s*\d+\s*\)\s*;?\s*$/', $text)) {
                continue;
            }

            // FIXED: Much more conservative filtering - only skip obvious standalone navigation
            if (preg_match('/^(目录|上一章|下一章|首页|书签|设置|夜间|返回|收藏本站|Copyright|69书吧)$/', $text)) {
                continue;
            }

            // FIXED: Reduced minimum length to capture short dialogue and descriptions
            if (strlen($text) < 2) {
                continue;
            }

            // FIXED: Only skip if the entire paragraph is just navigation (not partial matches)
            if (preg_match('/^(第\d+章|章节目录|全文阅读|TXT下载)$/', $text)) {
                continue;
            }

            // FIXED: Include everything else - be very inclusive to prevent content loss
            $contentParagraphs[] = $text;
        }

        // Preserve paragraph structure by joining with double newlines
        $content = implode("\n\n", $contentParagraphs);
        $this->log("Strategy 1 extracted " . count($contentParagraphs) . " paragraphs, " . strlen($content) . " characters");

        return $content;
    }

    /**
     * Strategy 2: Text node extraction from body
     */
    private function extractContentStrategy2(DOMDocument $dom): string {
        $xpath = new DOMXPath($dom);

        // Try to find the main content area
        $contentSelectors = [
            '//div[contains(@class, "content")]',
            '//div[contains(@class, "chapter")]',
            '//div[contains(@class, "text")]',
            '//div[contains(@id, "content")]',
            '//div[contains(@id, "chapter")]',
            '//main',
            '//article'
        ];

        foreach ($contentSelectors as $selector) {
            $elements = $xpath->query($selector);
            if ($elements->length > 0) {
                foreach ($elements as $element) {
                    $text = $this->cleanText($element->textContent);
                    if (strlen($text) > 100) { // FIXED: Reduced from 500 to 100 for substantial content
                        $this->log("Strategy 2 found content in: " . $selector);
                        return $this->formatExtractedText($text);
                    }
                }
            }
        }

        // FIXED: More conservative fallback extraction from body
        $body = $xpath->query('//body')->item(0);
        if ($body) {
            $text = $this->cleanText($body->textContent);

            // Remove JavaScript code like "loadAdv(2, 0);" at the beginning
            $text = preg_replace('/^loadAdv\s*\(\s*\d+\s*,\s*\d+\s*\)\s*;?\s*\n?/', '', $text);

            // FIXED: More conservative navigation removal - only remove at line endings
            $text = preg_replace('/\n(目录|上一章|下一章|首页|书签|设置|夜间|返回|收藏本站|69书吧|Copyright)[^\n]*$/m', '', $text);
            // FIXED: More conservative chapter title extraction - don't remove everything before it
            if (preg_match('/第\d+章[^。！？]*[。！？](.+)/s', $text, $matches)) {
                $text = $matches[1];
            }

            $this->log("Strategy 2 extracted from body: " . strlen($text) . " characters");
            return $this->formatExtractedText($text);
        }

        return '';
    }

    /**
     * Strategy 3: Raw text extraction with pattern matching
     */
    private function extractContentStrategy3(DOMDocument $dom): string {
        $allText = $dom->textContent;

        // Remove JavaScript code like "loadAdv(2, 0);" at the beginning
        $allText = preg_replace('/^loadAdv\s*\(\s*\d+\s*,\s*\d+\s*\)\s*;?\s*\n?/', '', $allText);

        // Try to find chapter content between markers
        $patterns = [
            '/第\d+章[^。！？]*[。！？](.+?)(?:上一章|下一章|目录|收藏|Copyright|69书吧)/s',
            '/第\d+章(.+?)(?:上一章|下一章|目录|收藏|Copyright|69书吧)/s',
            '/章[^。！？]*[。！？](.+?)(?:上一章|下一章|目录|收藏|Copyright|69书吧)/s'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $allText, $matches)) {
                $content = $this->cleanText($matches[1]);
                if (strlen($content) > 50) { // FIXED: Reduced from 200 to 50
                    $this->log("Strategy 3 successful with pattern: " . substr($pattern, 0, 50) . "...");
                    return $this->formatExtractedText($content);
                }
            }
        }

        // FIXED: Last resort with more conservative extraction
        if (preg_match('/第\d+章[^。！？]*[。！？](.+)/s', $allText, $matches)) {
            $content = $this->cleanText($matches[1]);
            // FIXED: More conservative footer removal - only remove if at the very end
            $content = preg_replace('/\n?(上一章|下一章|目录|收藏|Copyright|69书吧).*$/s', '', $content);
            $content = trim($content);

            if (strlen($content) > 30) { // FIXED: Reduced from 100 to 30
                $this->log("Strategy 3 last resort successful: " . strlen($content) . " characters");
                return $this->formatExtractedText($content);
            }
        }

        $this->log("Strategy 3 failed to extract meaningful content", 'error');
        return '';
    }

    /**
     * Format extracted text into proper paragraphs with Chinese character support
     */
    private function formatExtractedText(string $text): string {
        $this->log("formatExtractedText input length: " . mb_strlen($text, 'UTF-8') . " characters");

        // Ensure proper UTF-8 encoding
        if (!mb_check_encoding($text, 'UTF-8')) {
            $encoding = mb_detect_encoding($text, ['UTF-8', 'GB2312', 'GBK', 'BIG5'], true);
            if ($encoding) {
                $text = mb_convert_encoding($text, 'UTF-8', $encoding);
            }
        }

        // Remove JavaScript code like "loadAdv(2, 0);" at the beginning
        $text = preg_replace('/^loadAdv\s*\(\s*\d+\s*,\s*\d+\s*\)\s*;?\s*\n?/', '', $text);

        // Clean up excessive whitespace but preserve Chinese text structure
        $text = preg_replace('/[ \t]+/', ' ', $text); // Clean up spaces/tabs

        // Split into lines and process for proper paragraph formatting
        $lines = explode("\n", $text);
        $processedLines = [];
        $previousLineEmpty = false;

        foreach ($lines as $line) {
            $line = trim($line);

            if (!empty($line)) {
                // Add the line
                $processedLines[] = $line;

                // Check if this line ends with Chinese punctuation that indicates paragraph end
                if (preg_match('/[。！？]$/', $line)) {
                    // Add paragraph break after sentence-ending punctuation
                    $processedLines[] = '';
                    $previousLineEmpty = true;
                } else {
                    $previousLineEmpty = false;
                }
            } else {
                // Only add empty line if the previous line wasn't already followed by an empty line
                if (!$previousLineEmpty) {
                    $processedLines[] = '';
                    $previousLineEmpty = true;
                }
            }
        }

        // Join lines back together
        $text = implode("\n", $processedLines);

        // Final cleanup - ensure proper paragraph spacing
        $text = preg_replace('/\n{3,}/', "\n\n", $text); // Max 2 newlines for paragraph breaks
        $text = trim($text);

        $this->log("formatExtractedText output length: " . strlen($text));
        return $text;
    }

    /**
     * Extract chapter text content (legacy method for other content structures)
     */
    private function extractChapterText(DOMElement $contentElement): string {
        $content = '';

        // First try to find paragraphs within the content element
        $xpath = new DOMXPath($contentElement->ownerDocument);
        $paragraphs = $xpath->query('.//p', $contentElement);

        if ($paragraphs->length > 0) {
            foreach ($paragraphs as $paragraph) {
                $text = $this->cleanText($paragraph->textContent);
                if (!empty($text)) {
                    $content .= $text . "\n\n";
                }
            }
        } else {
            // Fallback: process text nodes and br tags within the content element
            foreach ($contentElement->childNodes as $node) {
                if ($node->nodeType === XML_TEXT_NODE) {
                    $text = trim($node->textContent);
                    if (!empty($text)) {
                        $content .= $text;
                    }
                } elseif ($node->nodeType === XML_ELEMENT_NODE) {
                    switch ($node->nodeName) {
                        case 'br':
                            $content .= "\n";
                            break;
                        case 'p':
                        case 'div':
                            $text = $this->cleanText($node->textContent);
                            if (!empty($text)) {
                                $content .= $text . "\n\n";
                            }
                            break;
                        default:
                            $text = $this->cleanText($node->textContent);
                            if (!empty($text)) {
                                $content .= $text;
                            }
                            break;
                    }
                }
            }
        }

        // If still no content, try getting all text from the element
        if (empty($content)) {
            $content = $this->cleanText($contentElement->textContent);
        }

        // Clean up the content
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        $content = trim($content);

        return $content;
    }

    /**
     * Get alternative chapter URL format for 69shuba
     */
    private function getAlternativeChapterUrl(string $url): string {
        // Try different URL formats that might work
        if (preg_match('/\/txt\/(\d+)\/(\d+)/', $url, $matches)) {
            $bookId = $matches[1];
            $chapterId = $matches[2];

            // Try with .htm extension
            if (!str_ends_with($url, '.htm')) {
                return "https://69shuba.cx/txt/{$bookId}/{$chapterId}.htm";
            }

            // Try without .htm extension
            if (str_ends_with($url, '.htm')) {
                return "https://69shuba.cx/txt/{$bookId}/{$chapterId}";
            }
        }

        return $url; // Return original if no alternative found
    }

    /**
     * Get the correct base URL from the original URL
     */
    private function getBaseUrlFromOriginal(string $url): string {
        $parsedUrl = parse_url($url);
        if ($parsedUrl && isset($parsedUrl['scheme']) && isset($parsedUrl['host'])) {
            return $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
        }

        return self::BASE_URL;
    }

    /**
     * Debug method to test chapter extraction step by step
     */
    public function debugChapterExtraction(string $chapterUrl): array {
        $debug = [];

        try {
            $debug['url'] = $chapterUrl;
            $debug['step'] = 'Making HTTP request';

            // Make request with cookies
            $options = [
                CURLOPT_TIMEOUT => 30,
                CURLOPT_COOKIEJAR => sys_get_temp_dir() . '/69shuba_cookies.txt',
                CURLOPT_COOKIEFILE => sys_get_temp_dir() . '/69shuba_cookies.txt'
            ];

            $html = $this->makeRequest($chapterUrl, $options);
            $debug['html_length'] = strlen($html);
            $debug['html_preview'] = substr($html, 0, 500);

            // Check for anti-bot protection
            $debug['anti_bot_detected'] = strpos($html, 'Just a moment') !== false;
            $debug['blocked_detected'] = strpos($html, 'class="newlistbox"') !== false;

            if ($debug['anti_bot_detected'] || $debug['blocked_detected']) {
                $debug['step'] = 'Anti-bot protection detected';
                return $debug;
            }

            $debug['step'] = 'Parsing HTML';
            $dom = $this->parseHtml($html);

            // Check page structure
            $xpath = new DOMXPath($dom);
            $debug['mybox_count'] = $xpath->query('//div[contains(@class, "mybox")]')->length;
            $debug['txtnav_count'] = $xpath->query('//div[contains(@class, "txtnav")]')->length;
            $debug['page1_count'] = $xpath->query('//div[contains(@class, "page1")]')->length;
            $debug['paragraph_count'] = $xpath->query('//p')->length;

            // Get page title
            $titleElements = $xpath->query('//title');
            $debug['page_title'] = $titleElements->length > 0 ? $titleElements->item(0)->textContent : 'No title';

            $debug['step'] = 'Extracting chapter title';
            $title = $this->extractChapterTitle($dom);
            $debug['chapter_title'] = $title;

            $debug['step'] = 'Extracting content using 69Shuba strategy';
            $content = $this->extractContentStrategy69Shuba($dom);
            $debug['strategy_69shuba_length'] = strlen($content);
            $debug['strategy_69shuba_preview'] = substr($content, 0, 200);

            if (empty($content) || strlen($content) < 200) {
                $debug['step'] = 'Trying strategy 1';
                $content = $this->extractContentStrategy1($dom);
                $debug['strategy_1_length'] = strlen($content);
                $debug['strategy_1_preview'] = substr($content, 0, 200);
            }

            if (empty($content) || strlen($content) < 200) {
                $debug['step'] = 'Trying strategy 2';
                $content = $this->extractContentStrategy2($dom);
                $debug['strategy_2_length'] = strlen($content);
                $debug['strategy_2_preview'] = substr($content, 0, 200);
            }

            if (empty($content) || strlen($content) < 200) {
                $debug['step'] = 'Trying strategy 3';
                $content = $this->extractContentStrategy3($dom);
                $debug['strategy_3_length'] = strlen($content);
                $debug['strategy_3_preview'] = substr($content, 0, 200);
            }

            $debug['final_content_length'] = strlen($content);
            $debug['final_content_preview'] = substr($content, 0, 300);
            $debug['final_content_ending'] = substr($content, -300);

            $debug['step'] = 'Complete';

        } catch (Exception $e) {
            $debug['error'] = $e->getMessage();
            $debug['step'] = 'Error occurred';
        }

        return $debug;
    }
}
